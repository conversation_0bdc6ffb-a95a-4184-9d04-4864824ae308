"use client";
import React from "react";
import { DetailedModal } from "@/components/Modal";
import { SubmitButton } from "@/components/button";
import { FaRegCircleCheck } from "react-icons/fa6";

interface UserAddedSuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddAnother: () => void;
  userName: string;
}

const UserAddedSuccessModal: React.FC<UserAddedSuccessModalProps> = ({
  isOpen,
  onClose,
  onAddAnother,
  userName,
}) => {
  return (
    <DetailedModal isOpen={isOpen} onClose={onClose} showCloseButton={false}>
      <div className="w-full text-center py-8">
        {/* Success Icon */}
        <div className="flex justify-center mb-6">
          <div className="flex items-center justify-center">
            <FaRegCircleCheck size={120} className="text-[#A3A3A3]" />
          </div>
        </div>

        {/* Success Message */}
        <div className="mb-8">
          <h2 className="text-lg lg:text-xl text-white font-medium mb-4">
            User Added Successfully
          </h2>
          <div className="text-sm text-[#A3A3A3]">
            <p className="">
              {userName} has been granted access to this platform. <br />{" "}
              They&apos;ll receive an email with steps to log in and get
              started.
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-4">
          <div className="flex justify-center">
            <SubmitButton label="Done" onClick={onClose} />
          </div>

          <button
            onClick={onAddAnother}
            className="text-[#E4E7EC] text-sm underline hover:text-white transition-colors"
          >
            Add Another User
          </button>
        </div>
      </div>
    </DetailedModal>
  );
};

export default UserAddedSuccessModal;

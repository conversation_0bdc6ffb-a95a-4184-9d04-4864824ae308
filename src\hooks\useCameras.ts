import { useState, useEffect } from "react";
import { Camera, transformApiCamera } from "@/lib/cameras";
import { cameraService, CreateCameraData, UpdateCameraData, ManualCameraData, ApiCamera } from "@/utils/cameraServices";
import toast from "react-hot-toast";
import { showDetailedErrorToast } from "@/components/ErrorToast";

interface ApiResponse {
  data?: ApiCamera[];
  cameras?: ApiCamera[];
  result?: ApiCamera[];
}

interface ApiError {
  response?: {
    status?: number;
    data?: {
      message?: string;
      errors?: string[];
      error?: string;
    };
  };
  message?: string;
}

export const useCameras = () => {
  const [addedCameras, setAddedCameras] = useState<Camera[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchCameras();
  }, []);

  const fetchCameras = async () => {
    try {
      setLoading(true);
      setError(null);
      const apiResponse = await cameraService.getAllCameras();
      let apiCameras: ApiCamera[];
      if (Array.isArray(apiResponse)) {
        apiCameras = apiResponse;
      } else {
        const response = apiResponse as ApiResponse;
        if (response.data && Array.isArray(response.data)) {
          apiCameras = response.data;
        } else if (response.cameras && Array.isArray(response.cameras)) {
          apiCameras = response.cameras;
        } else if (response.result && Array.isArray(response.result)) {
          apiCameras = response.result;
        } else {
          apiCameras = [];
        }
      }
      const transformedCameras = apiCameras.map(transformApiCamera);
      setAddedCameras(transformedCameras);
    } catch (err: unknown) {
      const error = err as Error;
      console.error("Error fetching cameras:", error);
      setError(error.message || "Failed to fetch cameras");
      setAddedCameras([]);
    } finally {
      setLoading(false);
    }
  };

  const addCamera = async (cameraData: CreateCameraData): Promise<Camera | null> => {
    try {
      setLoading(true);
      const newApiCamera = await cameraService.createCamera(cameraData);
      const newCamera = transformApiCamera(newApiCamera);
      setAddedCameras((prev) => [...prev, newCamera]);
      toast.success("Camera added successfully!");
      return newCamera;
    } catch (err: unknown) {
      const error = err as ApiError;
      console.error("Error adding camera:", error);
      const errorMessage = error.response?.data?.message || error.message || "Failed to add camera";
      setError(errorMessage);
      showDetailedErrorToast(errorMessage, error.response?.data?.errors || []);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const addCameraManually = async (manualData: ManualCameraData): Promise<Camera | null> => {
    try {
      setLoading(true);
      console.log("Attempting to add camera manually:", manualData);

      const newApiCamera = await cameraService.addCameraManually(manualData);
      const newCamera = transformApiCamera(newApiCamera);
      setAddedCameras((prev) => [...prev, newCamera]);
      toast.success("Camera connected successfully!");
      return newCamera;
    } catch (err: unknown) {
      const error = err as ApiError;
      console.error("Error adding camera manually:", error);

      const errorData = error.response?.data || {};
      const statusCode = error.response?.status;

      let mainMessage = "Failed to add camera";
      let specificErrors: string[] = [];
      if (statusCode === 404 || statusCode === 400) {
        mainMessage = "No camera found at this IP address";
        specificErrors = [
          "Camera may be offline or unreachable",
          "Check IP address and network connection",
          "Verify camera credentials"
        ];
      } else if (statusCode === 401 || statusCode === 403) {
        mainMessage = "Authentication failed";
        specificErrors = [
          "Invalid username or password",
          "Check camera credentials"
        ];
      } else if (statusCode === 408 || statusCode === 504) {
        mainMessage = "Connection timeout";
        specificErrors = [
          "Camera is not responding",
          "Check network connectivity"
        ];
      } else {
        mainMessage = errorData.message || error.message || "Failed to add camera manually";
        specificErrors = Array.isArray(errorData.errors) ? errorData.errors :
                        errorData.error ? [errorData.error] : [];
      }

      setError(mainMessage);
      showDetailedErrorToast(mainMessage, specificErrors);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const removeCamera = async (cameraId: string): Promise<boolean> => {
    try {
      setLoading(true);
      await cameraService.deleteCamera(cameraId);
      setAddedCameras((prev) => prev.filter((camera) => camera.id !== cameraId));
      toast.success("Camera removed successfully!");
      return true;
    } catch (err: unknown) {
      const error = err as ApiError;
      console.error("Error removing camera:", error);
      const errorMessage = error.response?.data?.message || error.message || "Failed to remove camera";
      setError(errorMessage);
      showDetailedErrorToast(errorMessage, error.response?.data?.errors || []);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const updateCamera = async (cameraId: string, updatedData: UpdateCameraData): Promise<Camera | null> => {
    try {
      setLoading(true);
      const updatedApiCamera = await cameraService.updateCamera(cameraId, updatedData);
      const updatedCamera = transformApiCamera(updatedApiCamera);
      setAddedCameras((prev) =>
        prev.map((camera) =>
          camera.id === cameraId ? updatedCamera : camera
        )
      );
      toast.success("Camera updated successfully!");
      return updatedCamera;
    } catch (err: unknown) {
      const error = err as ApiError;
      console.error("Error updating camera:", error);
      const errorMessage = error.response?.data?.message || error.message || "Failed to update camera";
      setError(errorMessage);
      showDetailedErrorToast(errorMessage, error.response?.data?.errors || []);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const checkCameraConnection = async (cameraId: string) => {
    try {
      const result = await cameraService.checkCameraConnection(cameraId);
      toast.success(`Connection check: ${result.message}`);
      return result;
    } catch (err: unknown) {
      const error = err as ApiError;
      console.error("Error checking camera connection:", error);
      const errorMessage = error.response?.data?.message || error.message || "Failed to check connection";
      showDetailedErrorToast(errorMessage, error.response?.data?.errors || []);
      return null;
    }
  };

  const refreshCameras = () => {
    fetchCameras();
  };

  return {
    addedCameras,
    loading,
    error,
    addCamera,
    addCameraManually,
    removeCamera,
    updateCamera,
    checkCameraConnection,
    refreshCameras,
    fetchCameras,
  };
};
"use client";
import React, { useEffect, useMemo } from "react";
import { TbLogout2 } from "react-icons/tb";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { IoClose } from "react-icons/io5";
import { SidebarProps } from "@/types/dashboardTypes";
import { navGroups } from "@/lib/navItems";
import { useAuthStore } from "@/store/authStore";
import { authServices } from "@/utils/authServices";
import { showDetailedErrorToast } from "@/components/ErrorToast";

const Sidebar = ({ onClose }: SidebarProps) => {
  const pathname = usePathname();
  const [activeNav, setActiveNav] = React.useState(pathname);
  const { clearAuth } = useAuthStore();

  useEffect(() => {
    setActiveNav(pathname);
  }, [pathname]);

  const navItems = useMemo(() => {
    return navGroups.map((group) => (
      <div key={group.tag}>
        <h2 className="text-sm text-[#8A8A8A] tracking-wide mb-2">
          {group.tag}
        </h2>
        <div className="space-y-2">
          {group.items.map((item) => {
            // Mark the nav item as active if the current pathname matches the href or is a nested route
            const isActive =
              pathname.startsWith(item.href) || activeNav === item.href;
            const imageSrc =
              isActive && item.activeImage ? item.activeImage : item.image;

            return (
              <Link
                key={item.href}
                href={item.href}
                aria-current={isActive ? "page" : undefined}
                className={`flex items-center cursor-pointer p-2 text-sm rounded ${
                  isActive
                    ? "py-3 border bg-[#1F1F1F] text-white border-solid gradient-border"
                    : "text-[#8A8A8A]"
                }`}
                onClick={onClose}
              >
                {item.icon ? (
                  <item.icon className="mr-2 text-lg" />
                ) : imageSrc ? (
                  <Image
                    src={imageSrc}
                    alt={`${item.label} Icon`}
                    width={20}
                    height={20}
                    className="mr-2"
                  />
                ) : null}
                <span>{item.label}</span>
              </Link>
            );
          })}
        </div>
      </div>
    ));
  }, [pathname, activeNav, onClose]);

  const handleLogout = async () => {
    try {
      await authServices.logout();
    } catch (error: unknown) {
      console.error("Logout error:", error);
      const errorObj = error as {
        response?: { data?: { message?: string; errors?: string[] } };
        message?: string;
      };
      const errorData = errorObj.response?.data || {
        message: errorObj.message || "Logout failed",
        errors: [],
      };
      const errorMsg = errorData.message || "Failed to logout";
      const specificErrors = Array.isArray(errorData.errors)
        ? errorData.errors
        : [];
      showDetailedErrorToast(errorMsg, specificErrors);
    } finally {
      clearAuth(); // Always clear client-side auth state
      window.location.href = "/auth"; // Redirect to login page
    }
  };

  return (
    <div className="w-full bg-[#121212] py-10 px-8 h-screen flex flex-col lg:border-r border-[#3D3D3D]">
      <div className="flex justify-between items-center mb-8 border-b border-[#3D3D3D] pb-7 lg:border-b lg:pb-7">
        <div className="flex items-center">
          <Image
            src="/teletraan.svg"
            alt="Teletraan Logo"
            width={30}
            height={30}
          />
          <h1 className="text-2xl ml-2 teletraan-title 2xl:text-2xl font-light tracking-wider">
            TELETRAAN
          </h1>
        </div>
        <div className="lg:hidden text-white" onClick={onClose}>
          <IoClose size={24} />
        </div>
      </div>
      <div className="flex-1 overflow-y-auto h-[calc(100vh-220px)]">
        <nav className="space-y-4">{navItems}</nav>
        <div className="w-full mt-14 mb-5 border p-3 border-solid gradient-border">
          <div>
            <Image
              src="/tourimg.svg"
              alt="Tour Image"
              width={280}
              height={32}
              className="mb-2 w-full h-auto"
            />
          </div>
          <div className="mt-4 border-b border-dashed border-[#575757] pb-4">
            <h2 className="text-sm mb-2">New to Teletraan?</h2>
            <p className="text-sm text-[#A3A3A3]">
              A brief tour will guide you through the essentials
            </p>
          </div>
          <div className="flex flex-col sm:flex-row justify-around items-center mt-6 mb-2 space-y-2 sm:space-y-0 sm:space-x-2">
            <div className="w-full sm:w-1/2">
              <button
                type="submit"
                className="w-full bg-[#1F1F1F] cursor-pointer font-medium shadow-[0px_0px_4px_0px_#242424_inset,_4px_4px_4px_0px_#00000040] border border-[#262626] text-white py-3 transition-colors"
              >
                Not Now
              </button>
            </div>
            <div className="w-full sm:w-1/2">
              <button
                type="submit"
                className="w-full bg-[#A3A3A3] cursor-pointer font-medium shadow-[4px_4px_4px_0px_#707070_inset,_0px_0px_5px_0px_#A3A3A380] border border-[#242424] text-[#262626] py-3 transition-colors"
              >
                Start Tour
              </button>
            </div>
          </div>
        </div>
      </div>
      <div className="mt-auto pt-4">
        <button
          onClick={handleLogout}
          className="flex cursor-pointer items-center text-[#8A8A8A]"
        >
          <TbLogout2 size={20} className="mr-2" />
          <span>Log Out</span>
        </button>
      </div>
    </div>
  );
};

export default React.memo(
  Sidebar,
  (prevProps, nextProps) => prevProps.onClose === nextProps.onClose
);

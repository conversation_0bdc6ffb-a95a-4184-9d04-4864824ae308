<svg width="613" height="788" viewBox="0 0 613 788" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_151_19)">
<circle cx="414" cy="618" r="223" fill="#3D3D3D"/>
</g>
<defs>
<filter id="filter0_f_151_19" x="-203.69" y="0.309753" width="1235.38" height="1235.38" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="197.345" result="effect1_foregroundBlur_151_19"/>
</filter>
</defs>
</svg>

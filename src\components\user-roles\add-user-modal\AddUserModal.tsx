"use client";
import React, { useState } from "react";
import { DetailedModal } from "@/components/Modal";
import { AddUserModalProps, User } from "@/types/userTypes";
import UserAddedSuccessModal from "../UserAddedSuccessModal";
import UserForm from "./UserForm";

const AddUserModal: React.FC<AddUserModalProps> = ({ isOpen, onClose, onUserAdded, onSuccessComplete, editUser }) => {
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [addedUserName, setAddedUserName] = useState("");

  const isEditMode = !!editUser;

  const handleUserSubmit = async (userData: User) => {
    try {
      const success = await onUserAdded(userData);

      if (success) {
        if (isEditMode) {
          // For edit mode: directly close modal
          handleModalClose();
        } else {
          // For add mode: show success modal only if API call succeeded
          setAddedUserName(userData.name);
          setShowSuccessModal(true);
        }
      }
      // If success is false, keep the modal open so user can see the error toast and try again
    } catch (error) {
      // If there's an exception, keep the modal open so user can see the error and try again
      console.error("Error in handleUserSubmit:", error);
    }
  };

  const handleModalClose = () => {
    setShowSuccessModal(false);
    onClose();
  };

  const handleSuccessClose = () => {
    setShowSuccessModal(false);
    onSuccessComplete();
  };

  const handleAddAnother = () => {
    setShowSuccessModal(false);
    setAddedUserName("");
    // Keep the main modal open for adding another user
  };

  return (
    <>
      <DetailedModal isOpen={isOpen && !showSuccessModal} onClose={handleModalClose}>
        <div className="w-full">
          <div className="mb-6 border-b border-[#3D3D3D] pb-4">
            <h2 className="text-lg lg:text-xl text-white font-medium mb-2">
              {isEditMode ? "Edit User Info" : "Add New User"}
            </h2>
            <p className="text-sm text-[#A3A3A3]">
              {isEditMode
                ? "Update user details and permissions"
                : "Add trusted users and define their level of control"
              }
            </p>
          </div>

          <UserForm
            editUser={editUser}
            onSubmit={handleUserSubmit}
            isEditMode={isEditMode}
          />
        </div>
      </DetailedModal>

      {!isEditMode && (
        <UserAddedSuccessModal
          isOpen={showSuccessModal}
          onClose={handleSuccessClose}
          onAddAnother={handleAddAnother}
          userName={addedUserName}
        />
      )}
    </>
  );
};

export default AddUserModal;

"use client";
import React, { useState, useRef, useEffect } from "react";
import { BsThreeDots } from "react-icons/bs";
import { FiEdit2, FiTrash2 } from "react-icons/fi";

interface UserActionsDropdownProps {
  onEditUser: () => void;
  onRemoveUser: () => void;
  userName: string;
}

const UserActionsDropdown: React.FC<UserActionsDropdownProps> = ({
  onEditUser,
  onRemoveUser,
  userName,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleToggle = () => {
    setIsOpen(!isOpen);
  };

  const handleEditUser = () => {
    setIsOpen(false);
    onEditUser();
  };

  const handleRemoveUser = () => {
    setIsOpen(false);
    onRemoveUser();
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        type="button"
        onClick={handleToggle}
        className="text-[#8A8A8A] cursor-pointer hover:text-[#E4E7EC] transition-colors p-1"
        aria-label={`More actions for ${userName}`}
      >
        <BsThreeDots size={16} />
      </button>

      {isOpen && (
        <div className="absolute right-0 top-full mt-1 bg-[#2E2E2E] border border-[#3D3D3D] shadow-lg z-50 min-w-[160px]">
          <button
            onClick={handleEditUser}
            className="w-full flex items-center px-4 py-3 text-sm text-[#E4E7EC] hover:bg-[#3D3D3D] transition-colors"
          >
            <FiEdit2 size={14} className="mr-3" />
            Edit User Info
          </button>
          <button
            onClick={handleRemoveUser}
            className="w-full flex items-center px-4 py-3 text-sm text-[#F04438] hover:bg-[#3D3D3D] transition-colors"
          >
            <FiTrash2 size={14} className="mr-3" />
            Remove User
          </button>
        </div>
      )}
    </div>
  );
};

export default UserActionsDropdown;

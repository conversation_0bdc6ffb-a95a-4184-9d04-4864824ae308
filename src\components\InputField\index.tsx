"use client";
import React, { useState, useEffect } from "react";
import { <PERSON>O<PERSON>lineEye, AiOutlineEyeInvisible } from "react-icons/ai";
import { HiOutlineUser } from "react-icons/hi2";
import { SlLock } from "react-icons/sl";
import { FormData } from "@/types/authtypes";

export const AuthInputField = ({
  id,
  label,
  type = "text",
  placeholder,
  value,
  onChange,
  onFocus,
  onBlur,
  error,
  isFocused,
  showToggle = false,
  isVisible = false,
  toggleVisibility,
  submitAttempted = false,
  readOnly = false,
}: {
  id: keyof FormData;
  label: string;
  type?: string;
  placeholder: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onFocus: (field: keyof FormData) => void;
  onBlur: () => void;
  error: string;
  isFocused: boolean;
  showToggle?: boolean;
  isVisible?: boolean;
  toggleVisibility?: () => void;
  submitAttempted?: boolean;
  readOnly?: boolean;
}) => (
  <div>
    <label htmlFor={id} className="block text-sm text-[#E4E7EC] mb-1">
      {label}
    </label>
    <div className="relative">
      <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
        {id === "email" ? (
          <HiOutlineUser color="#A3A3A3" size={18} />
        ) : (
          <SlLock color="#A3A3A3" size={18} />
        )}
      </div>
      <input
        type={showToggle ? (isVisible ? "text" : "password") : type}
        id={id}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        onFocus={() => onFocus(id)}
        onBlur={onBlur}
        readOnly={readOnly}
        className={`w-full ${
          readOnly
            ? "bg-[#2A2A2A] text-[#A3A3A3] cursor-not-allowed"
            : "bg-[#1F1F1F] text-white"
        } py-3 ${
          showToggle ? "pl-10 pr-10" : "pl-10 pr-4"
        } focus:outline-none focus:ring-1 ${
          error && (isFocused || submitAttempted)
            ? "border border-red-500 focus:ring-red-500"
            : "focus:ring-[#E4E7EC]"
        }`}
      />
      {showToggle && (
        <button
          type="button"
          onClick={toggleVisibility}
          className="absolute cursor-pointer right-3 top-1/2 -translate-y-1/2 text-[#E4E7EC] p-1 bg-[#2E2E2E] shadow-[4px_4px_4px_0px_#00000040_inset,_0px_0px_4px_0px_#242424]"
        >
          {isVisible ? (
            <AiOutlineEye size={20} />
          ) : (
            <AiOutlineEyeInvisible size={20} />
          )}
        </button>
      )}
    </div>
    {error && (isFocused || submitAttempted) && (
      <p className="text-red-500 text-xs mt-1 error-message">{error}</p>
    )}
  </div>
);

export const TextField = ({
  id,
  label,
  type = "text",
  placeholder,
  value,
  onChange,
  onFocus,
  onBlur,
  error,
  isFocused,
  submitAttempted = false,
  showErrorAlways = false,
}: {
  id: string;
  label: string;
  type?: string;
  placeholder: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onFocus: (field: string) => void;
  onBlur: () => void;
  error: string;
  isFocused: boolean;
  submitAttempted?: boolean;
  showErrorAlways?: boolean;
}) => {
  const shouldShowError = error && (showErrorAlways || isFocused || submitAttempted);

  return (
    <div>
      <label htmlFor={id} className="block text-sm text-[#E4E7EC] mb-1">
        {label}
      </label>
      <div className="relative">
        <input
          type={type}
          id={id}
          placeholder={placeholder}
          value={value}
          onChange={onChange}
          onFocus={() => onFocus(id)}
          onBlur={onBlur}
          className={`w-full bg-[#1F1F1F] text-white py-3 px-4 focus:outline-none focus:ring-1 ${
            shouldShowError
              ? "border border-red-500 focus:ring-red-500"
              : "focus:ring-[#E4E7EC]"
          }`}
        />
      </div>
      {shouldShowError && (
        <p className="text-red-500 text-xs mt-1 error-message">{error}</p>
      )}
    </div>
  );
};

export const EditableTextField = ({
  id,
  label,
  value,
  onChange,
  error,
  isFocused,
  submitAttempted,
  onFocus,
  onBlur,
}: {
  id: string;
  label: string;
  value: string;
  onChange: (value: string) => void;
  error: string;
  isFocused: boolean;
  submitAttempted: boolean;
  onFocus: (field: string) => void;
  onBlur: () => void;
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [inputValue, setInputValue] = useState(value);

  useEffect(() => {
    setInputValue(value);
  }, [value]);

  const handleEdit = () => {
    setIsEditing(true);
    setInputValue(value);
    console.log(`[EditableTextField] Edit clicked for ${id}`);
  };

  const handleDone = () => {
    console.log(`[EditableTextField] Done clicked for ${id}, new value: ${inputValue}`);
    onChange(inputValue);
    setIsEditing(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  return (
    <div className="relative">
      <TextField
        id={id}
        label={label}
        type="text"
        placeholder={`Enter ${label.toLowerCase()}`}
        value={isEditing ? inputValue : value}
        onChange={handleInputChange}
        onFocus={onFocus}
        onBlur={onBlur}
        error={error}
        isFocused={isFocused}
        submitAttempted={submitAttempted}
      />
      <button
        type="button"
        onClick={isEditing ? handleDone : handleEdit}
        className={`absolute cursor-pointer right-2 top-2/3 rounded-md -translate-y-1/2 text-sm px-4 py-[5px] transition-colors ${
          isEditing
            ? "bg-[#A3A3A3] text-[#242424]"
            : "bg-[#2E2E2E] text-[#E4E7EC]"
        }`}
      >
        {isEditing ? "Done" : "Edit"}
      </button>
    </div>
  );
};

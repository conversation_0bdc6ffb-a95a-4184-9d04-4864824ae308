import type { <PERSON>ada<PERSON> } from "next";
import { Lato } from "next/font/google";
import "./globals.css";
import { Toaster } from "react-hot-toast";

const lato = Lato({
  variable: "--font-lato",
  subsets: ["latin"],
  weight: ["100", "300", "400", "700", "900"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "TELETRAAN - Powered by AVZDAX",
  description:
    "Explore TELETRAAN, a cutting-edge platform by AVZDAX. Experience innovative technology and seamless performance.",
  icons: {
    icon: "/favicon.svg",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${lato.variable} antialiased`}
        suppressHydrationWarning={true}
      >
        {children}
        <Toaster
          position="top-right"
          toastOptions={{
            style: {
              background: "#1F1F1F",
              color: "#E4E7EC",
              border: "1px solid #3D3D3D",
            },
            duration: 3000,
          }}
        />
      </body>
    </html>
  );
}

import { useRouter, useParams } from "next/navigation";
import { useState, useEffect } from "react";
import { RecordedFootage, mockRecordedFootage } from "@/lib/recordedFootage";
import { formatDateTime } from "@/utils/dateUtils";

export const useDetailedFootageLogic = () => {
  const router = useRouter();
  const params = useParams();
  const { id } = params as { id: string };
  const [selectedFootage, setSelectedFootage] = useState<RecordedFootage | null>(null);
  const [loading, setLoading] = useState(true);

  const handleBack = () => {
    router.push("/dashboard/recorded-footage");
  };

  const handleDownload = () => {
    if (!selectedFootage) return;
    
    // Create a temporary link element to trigger download
    const link = document.createElement('a');
    link.href = selectedFootage.videoUrl;
    link.download = `${selectedFootage.cameraName}_${selectedFootage.startTime}.mp4`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  useEffect(() => {
    if (!id) {
      setLoading(false);
      return;
    }

    console.log("Dynamic Route ID:", id);

    // Find footage by ID from mock data
    // In a real app, this would be an API call
    const footage = mockRecordedFootage.find((item) => item.id === id);
    console.log("Found Footage:", footage);

    setSelectedFootage(footage || null);
    setLoading(false);
  }, [id]);

  return {
    selectedFootage,
    loading,
    handleBack,
    handleDownload,
    formatDateTime,
  };
};

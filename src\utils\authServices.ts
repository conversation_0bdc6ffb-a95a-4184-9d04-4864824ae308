import axios from "axios";
import Cookies from "js-cookie";

const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;

if (!apiBaseUrl) {
  throw new Error("NEXT_PUBLIC_API_BASE_URL is not defined in .env.local");
}

export const authServices = {
  signUp: async (data: {
    name: string;
    email: string;
    password: string;
    confirmPassword: string;
    role: string;
  }) => {
    const response = await axios.post(`${apiBaseUrl}/users`, data, {
      headers: { "Content-Type": "application/json" },
    });
    return response.data;
  },

  signIn: async (data: { email: string; password: string }) => {
    const response = await axios.post(`${apiBaseUrl}/auth/login`, data, {
      headers: { "Content-Type": "application/json" },
    });
    return response.data;
  },

  getUser: async (token: string) => {
    const response = await axios.get(`${apiBaseUrl}/auth/me`, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  },

  requestPasswordReset: async (email: string) => {
    const response = await axios.post(
      `${apiBaseUrl}/password/request`,
      { email },
      { headers: { "Content-Type": "application/json" } }
    );
    return response.data;
  },

  verifyOtp: async (email: string, otp: string) => {
    const response = await axios.post(
      `${apiBaseUrl}/otps/verify`,
      { email, otp },
      { headers: { "Content-Type": "application/json" } }
    );
    return response.data;
  },

  resetPassword: async (data: {
    email: string;
    otp: string;
    newPassword: string;
    confirmPassword: string;
  }) => {
    const response = await axios.post(`${apiBaseUrl}/password/reset`, data, {
      headers: { "Content-Type": "application/json" },
    });
    return response.data;
  },

  logout: async () => {
    const response = await axios.post(
      `${apiBaseUrl}/auth/logout`,
      {},
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${Cookies.get("authToken") || ""}`,
        },
      }
    );
    Cookies.remove("authToken");
    Cookies.remove("refreshToken");
    return response.data;
  },
};

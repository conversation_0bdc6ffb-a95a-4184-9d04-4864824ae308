"use client";
import React from "react";
import { DetailedModal } from "@/components/Modal";
import { User } from "@/types/userTypes";

interface RemoveUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: User | null;
  onConfirmRemove: (userId: string) => void;
}

const RemoveUserModal: React.FC<RemoveUserModalProps> = ({
  isOpen,
  onClose,
  user,
  onConfirmRemove,
}) => {
  const handleRemove = () => {
    if (!user) return;
    onConfirmRemove(user.id);
    onClose();
  };

  return (
    <DetailedModal showCloseButton={false} isOpen={isOpen} onClose={onClose}>
      <div className="mb-10">
        <h2 className="text-lg lg:text-xl text-white font-medium pb-4">
          You&apos;re about to remove this user from your team?
        </h2>
        <p className="text-[#A3A3A3] text-sm lg:text-base mb-4">
          You&apos;re about to remove this user from your team. They will
          immediately lose access to the platform and all associated
          permissions.
        </p>
      </div>
      <div className="flex justify-end space-x-4">
        <button
          className="bg-[#1F1F1F] cursor-pointer font-medium shadow-[0px_0px_4px_0px_#242424_inset,_4px_4px_4px_0px_#00000040] border border-[#262626] text-white px-4 py-2 transition-colors"
          onClick={onClose}
        >
          Cancel
        </button>
        <button
          className="bg-[#F04438] text-white px-4 py-2"
          onClick={handleRemove}
        >
          Yes, Remove User
        </button>
      </div>
    </DetailedModal>
  );
};

export default RemoveUserModal;

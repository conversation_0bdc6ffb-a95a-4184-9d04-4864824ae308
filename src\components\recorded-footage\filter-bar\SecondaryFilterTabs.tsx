"use client";
import React from "react";
import Image from "next/image";
import MultiSelectSubDropdown from "./MultiSelectSubDropdown";
import { SecondaryFilterTabsProps } from "@/types/filterTypes";
import { 
  FILTER_TABS, 
  MOCK_CAMERA_OPTIONS, 
  MOCK_LOCATION_OPTIONS, 
  MOCK_TAG_OPTIONS 
} from "./constants";

const SecondaryFilterTabs: React.FC<SecondaryFilterTabsProps> = ({
  activeTab,
  onTabChange,
  selectedCameras,
  selectedLocations,
  selectedTags,
  onToggleCamera,
  onToggleLocation,
  onToggleTag,
}) => {
  const getSubDropdownOptions = () => {
    switch (activeTab) {
      case "camera":
        return MOCK_CAMERA_OPTIONS;
      case "location":
        return MOCK_LOCATION_OPTIONS;
      case "tags":
        return MOCK_TAG_OPTIONS;
      default:
        return [];
    }
  };

  const getSelectedOptions = () => {
    switch (activeTab) {
      case "camera":
        return selectedCameras;
      case "location":
        return selectedLocations;
      case "tags":
        return selectedTags;
      default:
        return [];
    }
  };

  const getToggleHandler = () => {
    switch (activeTab) {
      case "camera":
        return onToggleCamera;
      case "location":
        return onToggleLocation;
      case "tags":
        return onToggleTag;
      default:
        return () => {};
    }
  };

  return (
    <div className="w-auto min-w-[50%] max-w-[50%]">
      <div className="bg-[#1F1F1F] p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-5">
            {FILTER_TABS.map((tab) => (
              <div
                key={tab.id}
                className={`cursor-pointer text-sm px-3 py-1 rounded transition-colors ${
                  tab.iconSrc ? "flex items-center gap-2" : ""
                } ${
                  activeTab === tab.id
                    ? "bg-[#2E2E2E] text-[#A3A3A3] text-xs gradient-border"
                    : "bg-[#3D3D3D] text-[#A3A3A3] text-xs"
                }`}
                onClick={() => onTabChange(tab.id)}
              >
                {tab.iconSrc && (
                  <Image
                    src={tab.iconSrc}
                    alt={`${tab.label} icon`}
                    width={16}
                    height={16}
                    className="w-4 h-4"
                  />
                )}
                {tab.label}
              </div>
            ))}
          </div>
        </div>
      </div>

      {activeTab !== "all" && (
        <MultiSelectSubDropdown
          options={getSubDropdownOptions()}
          selectedOptions={getSelectedOptions()}
          onToggleOption={getToggleHandler()}
          isVisible={true}
        />
      )}
    </div>
  );
};

export default SecondaryFilterTabs;

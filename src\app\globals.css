@import "tailwindcss";

:root {
  --background: #121212;
  --foreground: #ededed;
}

@theme {
  --breakpoint-xxl: 1440px;
  --breakpoint-xlg: 1680px;
  --breakpoint-maxLg: 1920px;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-lato);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: "Lato", Arial, Helvetica, sans-serif;
}

.teletraan-title {
  font-family: "Space Mono", monospace;
  font-weight: 400;
}

.gradient-border {
  border: 0.8px solid;
  border-image-source: linear-gradient(
    165.03deg,
    #707070 -24.58%,
    rgba(112, 112, 112, 0) 32.88%,
    rgba(191, 191, 191, 0.12) 64.59%,
    #bfbfbf 112.79%
  );
  border-image-slice: 1;
}

.grade {
  border: 0.8px solid;
  border-image-source: linear-gradient(
    170.05deg,
    #707070 0%,
    rgba(112, 112, 112, 0) 38.71%,
    rgba(228, 231, 236, 0.12) 60.07%,
    #e4e7ec 92.54%
  );
  border-image-slice: 1;
}

.gradient-border-x {
  border-image-source: linear-gradient(
    165.03deg,
    #707070 -24.58%,
    rgba(112, 112, 112, 0) 32.88%,
    rgba(191, 191, 191, 0.12) 64.59%,
    #bfbfbf 112.79%
  );
  border-image-slice: 1;
}

.gradient-border-two {
  border: 1px solid;
  border-image-source: linear-gradient(
    165.03deg,
    #707070 -24.58%,
    rgba(112, 112, 112, 0) 32.88%,
    rgba(191, 191, 191, 0.12) 64.59%,
    #bfbfbf 112.79%
  );
}

.gradient-border-three {
  border: none;
  border-radius: 2%;
  box-shadow: 0 0 0 0.8px #707070, inset 0 0 0 0.8px #bfbfbf;
  background-origin: border-box;
}

@keyframes teletraan-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0px 0px 20px 0px rgba(228, 231, 236, 0.8);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0px 0px 20px 0px rgba(228, 231, 236, 1);
  }
  100% {
    transform: scale(1);
    box-shadow: 0px 0px 20px 0px rgba(228, 231, 236, 0.8);
  }
}

@keyframes teletraan-pulse-line {
  0% {
    opacity: 0.4;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 0.4;
  }
}

@keyframes teletraan-pulse-box {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

.teletraan-pulse-circle {
  animation: teletraan-pulse 3s ease-in-out infinite;
}

.teletraan-pulse-element.line {
  animation: teletraan-pulse-line 3s ease-in-out infinite;
}

.teletraan-pulse-element.box {
  animation: teletraan-pulse-box 3s ease-in-out infinite;
  transform-origin: center;
}

.error-message {
  animation: fadeIn 0.3s ease-in;
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.camera-overlay {
  background: linear-gradient(
    145.42deg,
    rgba(31, 31, 31, 0.7) 32.16%,
    rgba(112, 112, 112, 0.7) 237.44%
  );
}

::-webkit-scrollbar {
  display: none;
}

* {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

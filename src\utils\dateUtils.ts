export const formatDate = (dateStr: string): string => {
  const date = new Date(dateStr);
  return date.toLocaleDateString("en-US", {
    month: "long",
    day: "2-digit",
    year: "numeric",
  });
};

export const formatTime = (dateStr: string): string => {
  const date = new Date(dateStr);
  return date.toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  });
};

export const formatDateTime = (dateStr: string): string => {
  const date = new Date(dateStr);
  return date.toLocaleString("en-US", {
    month: "short",
    day: "2-digit",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  });
};

export const isToday = (dateStr: string): boolean => {
  const today = new Date();
  const date = new Date(dateStr);
  return (
    date.getDate() === today.getDate() &&
    date.getMonth() === today.getMonth() &&
    date.getFullYear() === today.getFullYear()
  );
};

export const isYesterday = (dateStr: string): boolean => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  const date = new Date(dateStr);
  return (
    date.getDate() === yesterday.getDate() &&
    date.getMonth() === yesterday.getMonth() &&
    date.getFullYear() === yesterday.getFullYear()
  );
};

export const getRelativeDate = (dateStr: string): string => {
  if (isToday(dateStr)) {
    return "Today";
  }
  if (isYesterday(dateStr)) {
    return "Yesterday";
  }
  return formatDate(dateStr);
};

export const sortByDate = (a: string, b: string): number => {
  return new Date(b).getTime() - new Date(a).getTime();
};

export const isDateInRange = (
  date: Date,
  startDate: Date | null,
  endDate: Date | null
): boolean => {
  if (!startDate && !endDate) return true;

  const startOfDay = (d: Date) => new Date(d.getFullYear(), d.getMonth(), d.getDate());
  const endOfDay = (d: Date) => new Date(d.getFullYear(), d.getMonth(), d.getDate(), 23, 59, 59, 999);

  if (startDate && endDate) {
    const start = startOfDay(startDate);
    const end = endOfDay(endDate);
    return date >= start && date <= end;
  } else if (startDate) {
    const start = startOfDay(startDate);
    return date >= start;
  } else if (endDate) {
    const end = endOfDay(endDate);
    return date <= end;
  }

  return true;
};

export const formatDateRange = (startDate: Date | null, endDate: Date | null): string => {
  if (!startDate && !endDate) return "Date Range";
  if (startDate && !endDate) return `From ${startDate.toLocaleDateString()}`;
  if (!startDate && endDate) return `Until ${endDate.toLocaleDateString()}`;
  if (startDate && endDate) {
    return `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`;
  }
  return "Date Range";
};

import { User } from "@/types/userTypes";

export const mockUsers: User[] = [
  // Regular Users
  {
    id: "user-001",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "User",
    status: "Active",
    dateAdded: "December 15, 2024",
  },
  {
    id: "user-002",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "User",
    status: "Inactive",
    dateAdded: "December 10, 2024",
  },
  {
    id: "user-003",
    name: "<PERSON>",
    email: "<EMAIL>", 
    role: "User",
    status: "Pending",
    dateAdded: "December 18, 2024",
  },
  
  // Security Personnel
  {
    id: "security-001",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Security",
    status: "Active", 
    dateAdded: "December 5, 2024",
  },
  {
    id: "security-002",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Security",
    status: "Active",
    dateAdded: "December 8, 2024",
  },
  {
    id: "security-003",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "<PERSON>",
    status: "Inactive",
    dateAdded: "November 28, 2024",
  },

  // Admin Users
  {
    id: "admin-001",
    name: "<PERSON> <PERSON>",
    email: "<EMAIL>",
    role: "Admin",
    status: "Active",
    dateAdded: "December 1, 2024",
  },

  // Additional test users for scrolling
  {
    id: "user-004",
    name: "Alex Thompson",
    email: "<EMAIL>",
    role: "User",
    status: "Active",
    dateAdded: "December 20, 2024",
  },
  {
    id: "user-005",
    name: "Maria Rodriguez",
    email: "<EMAIL>",
    role: "User",
    status: "Inactive",
    dateAdded: "December 22, 2024",
  },
  {
    id: "security-004",
    name: "James Wilson",
    email: "<EMAIL>",
    role: "Security",
    status: "Active",
    dateAdded: "December 25, 2024",
  },
  {
    id: "user-006",
    name: "Jennifer Lee",
    email: "<EMAIL>",
    role: "User",
    status: "Active",
    dateAdded: "December 28, 2024",
  },
  {
    id: "security-005",
    name: "Kevin Brown",
    email: "<EMAIL>",
    role: "Security",
    status: "Inactive",
    dateAdded: "December 30, 2024",
  },
  {
    id: "user-007",
    name: "Amanda Clark",
    email: "<EMAIL>",
    role: "User",
    status: "Active",
    dateAdded: "January 2, 2025",
  },
  {
    id: "user-008",
    name: "Daniel Miller",
    email: "<EMAIL>",
    role: "User",
    status: "Inactive",
    dateAdded: "January 5, 2025",
  },
  {
    id: "security-006",
    name: "Rachel Green",
    email: "<EMAIL>",
    role: "Security",
    status: "Active",
    dateAdded: "January 8, 2025",
  },

  // Audit Personnel
  {
    id: "audit-001",
    name: "Alex Thompson",
    email: "<EMAIL>",
    role: "Audit",
    status: "Active",
    dateAdded: "January 10, 2025",
  },
  {
    id: "audit-002",
    name: "Maria Rodriguez",
    email: "<EMAIL>",
    role: "Audit",
    status: "Active",
    dateAdded: "January 12, 2025",
  },
];

// Helper function to get users by role
export const getUsersByRole = (role: "Admin" | "User" | "Security" | "Audit") => {
  return mockUsers.filter(user => user.role === role);
};

// Helper function to get users by status
export const getUsersByStatus = (status: "Active" | "Inactive" | "Pending") => {
  return mockUsers.filter(user => user.status === status);
};

// Helper function to get pending invitations count
export const getPendingInvitationsCount = () => {
  return mockUsers.filter(user => user.status === "Pending").length;
};

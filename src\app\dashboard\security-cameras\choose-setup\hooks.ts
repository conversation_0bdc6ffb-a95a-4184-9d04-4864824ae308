import { useState } from "react";
import { Camera } from "@/lib/cameras";
import { useCameras } from "@/hooks/useCameras";

export const useCameraSetup = (
  onClose: () => void,
  onMethodSelect?: (method: string) => void,
  onCameraAdded?: (camera: Camera) => void,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _addedCameras: Camera[] = []
) => {
  const { addCameraManually, loading } = useCameras();
  const [selectedMethod, setSelectedMethod] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState<
    "choose" | "manual" | "smart" | "success"
  >("choose");
  const [selectedCamera, setSelectedCamera] = useState<Camera | null>(null);
  const [error, setError] = useState<string>("");

  const handleMethodSelect = (method: string) => {
    setSelectedMethod(method);
    setError("");
    if (onMethodSelect) {
      onMethodSelect(method);
    }
  };

  const handleContinue = () => {
    if (selectedMethod === "manual") {
      setCurrentStep("manual");
    } else if (selectedMethod === "smart") {
      setCurrentStep("smart");
    }
  };

  const handleClose = () => {
    if (currentStep !== "success") {
      setSelectedMethod(null);
      setCurrentStep("choose");
      setSelectedCamera(null);
      setError("");
      onClose();
    }
  };

  const handleManualSetupCancel = () => {
    setCurrentStep("choose");
    setError("");
  };

  const handleManualSetupSubmit = async (formData: {
    ipAddress: string;
    location: string;
  }) => {
    try {
      setError("");

      const apiFormData = {
        name: `Camera-${formData.ipAddress}`,
        ipAddress: formData.ipAddress,
        username: "admin",
        password: "admin",
        port: 554,
        manufacturer: "Generic",
        model: "IP Camera",
        location: formData.location || "Unknown Location",
      };

      console.log("Sending manual camera data to API:", apiFormData);
      const newCamera = await addCameraManually(apiFormData);

      if (newCamera) {
        setSelectedCamera(newCamera);
        setCurrentStep("success");
        if (onCameraAdded) {
          onCameraAdded(newCamera);
        }
      }
    } catch (err: unknown) {
      const error = err as Error;
      setError(error.message || "Failed to add camera");
    }
  };

  const handleSuccessComplete = () => {
    setSelectedMethod(null);
    setCurrentStep("choose");
    setSelectedCamera(null);
    setError("");
    onClose();
  };

  return {
    selectedMethod,
    currentStep,
    setCurrentStep,
    selectedCamera,
    error,
    loading,
    handleMethodSelect,
    handleContinue,
    handleClose,
    handleManualSetupCancel,
    handleManualSetupSubmit,
    handleSuccessComplete,
  };
};
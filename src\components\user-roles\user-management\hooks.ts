import { useState, useEffect } from "react";
import { User } from "@/types/userTypes";
import { useAuthStore } from "@/store/authStore";
import { useRoleAccess } from "@/hooks/useRoleAccess";

export const useUserManagement = (users: User[]) => {
  // State management
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredUsers, setFilteredUsers] = useState<User[]>(users);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [isDateDropdownOpen, setIsDateDropdownOpen] = useState(false);
  const [isStatusDropdownOpen, setIsStatusDropdownOpen] = useState(false);
  const [isUserTypeDropdownOpen, setIsUserTypeDropdownOpen] = useState(false);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [mainAdmin, setMainAdmin] = useState<User | null>(null);
  const [isRemoveModalOpen, setIsRemoveModalOpen] = useState(false);
  const [userToRemove, setUserToRemove] = useState<User | null>(null);
  const [isMounted, setIsMounted] = useState(false);

  // Filter states
  const [selectedStatus, setSelectedStatus] = useState<string>("");
  const [selectedUserType, setSelectedUserType] = useState<string>("");
  const [selectedDateSort, setSelectedDateSort] = useState<string>("");

  // Auth and role access
  const { formData, isAuthenticated } = useAuthStore();
  const { userRole } = useRoleAccess();

  // Handle client-side mounting
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Get current user information from localStorage and auth store (client-side only)
  useEffect(() => {
    if (!isMounted) return; // Only run on client-side after mounting

    const getCurrentUser = () => {
      if (isAuthenticated && typeof window !== 'undefined') {
        // Try to get email from localStorage first (persisted)
        let userEmail = localStorage.getItem("userEmail");
        let userName = localStorage.getItem("userName");

        // If not in localStorage, try to get from auth store formData
        if (!userEmail && formData.email) {
          userEmail = formData.email;
          userName = formData.email.split("@")[0]; // Use part before @ as name

          // Persist to localStorage for future use
          localStorage.setItem("userEmail", userEmail);
          localStorage.setItem("userName", userName);
        }

        // Also save to localStorage if formData has email (for when user just logged in)
        if (formData.email && formData.email !== userEmail) {
          userEmail = formData.email;
          userName = formData.email.split("@")[0];
          localStorage.setItem("userEmail", userEmail);
          localStorage.setItem("userName", userName);
        }

        // Create current user object with actual role from auth store
        const currentDate = new Date();
        const currentUserRole = userRole === "admin" ? "Admin" :
                              userRole === "security" ? "Security" :
                              userRole === "audit" ? "Audit" : "User";

        const currentUserObj: User = {
          id: "current-user",
          name: userName || "Current User",
          email: userEmail || "<EMAIL>",
          role: currentUserRole,
          status: "Active",
          dateAdded: currentDate.toLocaleDateString("en-US", {
            year: "numeric",
            month: "long",
            day: "numeric",
          }),
        };

        console.log("Current user:", currentUserObj);
        setCurrentUser(currentUserObj);
      }
    };

    getCurrentUser();
  }, [isMounted, isAuthenticated, formData.email, userRole]);

  // Filter users based on search query and filters (exclude pending users from main table)
  useEffect(() => {
    let filtered = users.filter(user => user.status !== "Pending");

    // Apply search filter
    if (searchQuery.trim() !== "") {
      filtered = filtered.filter(
        (user) =>
          user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
          user.role.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply status filter
    if (selectedStatus && selectedStatus !== "") {
      filtered = filtered.filter(user => user.status === selectedStatus);
    }

    // Apply user type filter
    if (selectedUserType && selectedUserType !== "") {
      filtered = filtered.filter(user => user.role === selectedUserType);
    }

    // Apply date sorting
    if (selectedDateSort && selectedDateSort !== "") {
      filtered = [...filtered].sort((a, b) => {
        const dateA = new Date(a.dateAdded);
        const dateB = new Date(b.dateAdded);
        
        if (selectedDateSort === "Newest First") {
          return dateB.getTime() - dateA.getTime();
        } else if (selectedDateSort === "Oldest First") {
          return dateA.getTime() - dateB.getTime();
        }
        return 0;
      });
    }

    // Find the main admin (earliest admin by date) and current user
    const adminUsers = filtered.filter(user => user.role === "Admin");
    const mainAdmin = adminUsers.length > 0 
      ? adminUsers.reduce((earliest, current) => {
          const earliestDate = new Date(earliest.dateAdded);
          const currentDate = new Date(current.dateAdded);
          return currentDate < earliestDate ? current : earliest;
        })
      : null;

    // Remove main admin and current user from filtered list to avoid duplicates
    const otherUsers = filtered.filter(user => {
      if (mainAdmin && user.id === mainAdmin.id) return false;
      if (currentUser && user.email === currentUser.email) return false;
      return true;
    });

    // Store main admin and other users separately for proper ordering
    setFilteredUsers(otherUsers);
    
    // Store main admin for use in UserTable
    if (mainAdmin) {
      setMainAdmin(mainAdmin);
    } else {
      setMainAdmin(null);
    }
  }, [searchQuery, users, selectedStatus, selectedUserType, selectedDateSort, currentUser]);

  // Event handlers
  const handleUserSelect = (userId: string) => {
    setSelectedUsers((prev) =>
      prev.includes(userId)
        ? prev.filter((id) => id !== userId)
        : [...prev, userId]
    );
  };

  const handleSelectAll = () => {
    if (selectedUsers.length === filteredUsers.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers(filteredUsers.map((user) => user.id));
    }
  };

  const handleRemoveUser = (user: User) => {
    setUserToRemove(user);
    setIsRemoveModalOpen(true);
  };

  const handleConfirmRemove = (userId: string, onRemoveUser: (userId: string) => void) => {
    if (userId === "current-user") {
      console.error("Attempted to remove current admin user - this should not happen!");
      return;
    }
    onRemoveUser(userId);
    setIsRemoveModalOpen(false);
    setUserToRemove(null);
  };

  const handleCloseRemoveModal = () => {
    setIsRemoveModalOpen(false);
    setUserToRemove(null);
  };

  // Dropdown handlers
  const handleToggleDateDropdown = () => setIsDateDropdownOpen(!isDateDropdownOpen);
  const handleToggleStatusDropdown = () => setIsStatusDropdownOpen(!isStatusDropdownOpen);
  const handleToggleUserTypeDropdown = () => setIsUserTypeDropdownOpen(!isUserTypeDropdownOpen);

  return {
    // State
    searchQuery,
    filteredUsers,
    selectedUsers,
    currentUser,
    mainAdmin,
    isRemoveModalOpen,
    userToRemove,
    isMounted,
    selectedStatus,
    selectedUserType,
    selectedDateSort,
    isDateDropdownOpen,
    isStatusDropdownOpen,
    isUserTypeDropdownOpen,
    isAuthenticated,

    // Setters
    setSearchQuery,
    setSelectedStatus,
    setSelectedUserType,
    setSelectedDateSort,

    // Handlers
    handleUserSelect,
    handleSelectAll,
    handleRemoveUser,
    handleConfirmRemove,
    handleCloseRemoveModal,
    handleToggleDateDropdown,
    handleToggleStatusDropdown,
    handleToggleUserTypeDropdown,
  };
};

// Hook for SearchAndFilters component
export const useSearchAndFilters = () => {
  const [isInvitationsDropdownOpen, setIsInvitationsDropdownOpen] = useState(false);

  const handleToggleInvitationsDropdown = () => {
    setIsInvitationsDropdownOpen(!isInvitationsDropdownOpen);
  };

  const handleCloseInvitationsDropdown = () => {
    setIsInvitationsDropdownOpen(false);
  };

  return {
    // State
    isInvitationsDropdownOpen,

    // Handlers
    handleToggleInvitationsDropdown,
    handleCloseInvitationsDropdown,
  };
};

// Hook for InvitationsDropdown component
export const useInvitationsDropdown = () => {
  const [actionLoading, setActionLoading] = useState<{ [key: string]: 'revoke' | 'resend' | null }>({});

  const handleRevoke = async (invitationId: string, onRevoke: (id: string) => Promise<boolean>) => {
    if (!invitationId || invitationId === 'undefined') {
      console.error("Invalid invitation ID:", invitationId);
      return;
    }

    setActionLoading(prev => ({ ...prev, [invitationId]: 'revoke' }));
    try {
      await onRevoke(invitationId);
    } finally {
      setActionLoading(prev => ({ ...prev, [invitationId]: null }));
    }
  };

  const handleResend = async (invitationId: string, onResend: (id: string) => Promise<boolean>) => {
    if (!invitationId || invitationId === 'undefined') {
      console.error("Invalid invitation ID for resend:", invitationId);
      return;
    }

    setActionLoading(prev => ({ ...prev, [invitationId]: 'resend' }));
    try {
      await onResend(invitationId);
    } finally {
      setActionLoading(prev => ({ ...prev, [invitationId]: null }));
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const formatRole = (role: string) => {
    return role.charAt(0).toUpperCase() + role.slice(1);
  };

  return {
    // State
    actionLoading,

    // Handlers
    handleRevoke,
    handleResend,

    // Utilities
    formatDate,
    formatRole,
  };
};

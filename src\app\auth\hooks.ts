import { useAuthStore } from "@/store/authStore";
import { FormData, Errors } from "@/types/authtypes";
import { useCallback, useEffect, useState } from "react";
import { authServices } from "@/utils/authServices";
import Cookies from "js-cookie";
import { showDetailedErrorToast } from "@/components/ErrorToast";

import {
  getInvitationTokenFromUrl,
  validateInvitationToken,
  acceptInvitation,
  clearInvitationTokenFromUrl
} from "@/utils/invitationUtils";

export const useAuthValidation = () => {
  const {
    activeTab,
    setActiveTab,
    formData,
    setFormData,
    setErrors,
    focusedField,
    setFocusedField,
    setSubmitAttempted,
    setIsAuthenticated,
    setToken,
    setUserId,
    setUserRole,
    invitationData,
    setInvitationData,
  } = useAuthStore();

  const [isMounted, setIsMounted] = useState(false);
  const [invitationToken, setInvitationToken] = useState<string | null>(null);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Check for invitation token on mount
  useEffect(() => {
    if (!isMounted) return;

    const token = getInvitationTokenFromUrl();
    if (token) {
      setInvitationToken(token);
      // Validate the invitation token
      validateInvitationToken(token)
        .then((data) => {
          if (data) {
            setInvitationData(data);
            // Pre-fill invited user's email in the form - this will be read-only
            setFormData({ email: data.email });
            // Set active tab to Sign Up for invitation users
            setActiveTab("Sign Up");
            // Clear token from URL for security
            clearInvitationTokenFromUrl();
          }
        })
        .catch((error) => {
          // Show specific error message for different invitation statuses
          const errorMessage = error.message || "Invalid or expired invitation link";
          showDetailedErrorToast(errorMessage, []);
          console.error("Invitation validation error:", error);
        });
    }
  }, [isMounted, setInvitationData, setFormData, setActiveTab]);

  const validateEmail = useCallback((email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }, []);

  const validateForm = useCallback(
    (
      field: keyof FormData | null = null
    ): { isValid: boolean; newErrors: Errors } => {
      const newErrors: Errors = {
        email: "",
        password: "",
        confirmPassword: "",
        name: "",
        companyName: "",
      };
      let isValid = true;

      if (!field || field === "email") {
        if (!formData.email) {
          newErrors.email = "Email is required";
          isValid = false;
        } else if (!validateEmail(formData.email)) {
          newErrors.email = "Please enter a valid email address";
          isValid = false;
        }
      }

      if (!field || field === "password") {
        if (!formData.password) {
          newErrors.password = "Password is required";
          isValid = false;
        } else if (formData.password.length < 8) {
          newErrors.password = "Password must be 8 or more characters";
          isValid = false;
        }
      }

      if (activeTab === "Sign Up" && (!field || field === "confirmPassword")) {
        if (!formData.confirmPassword) {
          newErrors.confirmPassword = "Confirm Password is required";
          isValid = false;
        } else if (formData.password !== formData.confirmPassword) {
          newErrors.confirmPassword = "Passwords do not match";
          isValid = false;
        }
      }

      // For invitation signup, no additional fields are required
      // Just email and password validation (handled above)

      return { isValid, newErrors };
    },
    [activeTab, formData, validateEmail]
  );

  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>): void => {
      const { id, value } = e.target;
      setFormData({ [id]: value });

      if (focusedField) {
        const { newErrors } = validateForm(focusedField as keyof FormData);
        setErrors({
          email: focusedField === "email" ? newErrors.email : "",
          password: focusedField === "password" ? newErrors.password : "",
          confirmPassword:
            focusedField === "confirmPassword" ? newErrors.confirmPassword : "",
          name: focusedField === "name" ? newErrors.name : "",
          companyName: focusedField === "companyName" ? newErrors.companyName : "",
        });
      }
    },
    [focusedField, setFormData, setErrors, validateForm]
  );

  const handleFocus = useCallback(
    (field: keyof FormData): void => {
      setFocusedField(field);
      const { newErrors } = validateForm(field);
      setErrors({
        email: field === "email" ? newErrors.email : "",
        password: field === "password" ? newErrors.password : "",
        confirmPassword:
          field === "confirmPassword" ? newErrors.confirmPassword : "",
        name: field === "name" ? newErrors.name : "",
        companyName: field === "companyName" ? newErrors.companyName : "",
      });
    },
    [setFocusedField, setErrors, validateForm]
  );

  const handleBlur = useCallback((): void => {
    setFocusedField(null);
    setErrors({
      email: "",
      password: "",
      confirmPassword: "",
      name: "",
      companyName: "",
    });
  }, [setErrors, setFocusedField]);

  const handleSubmit = useCallback(
    async (e: React.FormEvent<HTMLFormElement>): Promise<boolean> => {
      e.preventDefault();
      const { isValid, newErrors } = validateForm();
      setErrors(newErrors);
      setSubmitAttempted(true);

      if (!isValid) {
        const { newErrors: allErrors } = validateForm();
        setErrors(allErrors);
        return false;
      }

      try {
        if (activeTab === "Sign Up") {
          // Check if this is an invitation signup
          if (invitationData && invitationToken) {
            // Get the original name from cookie (stored when admin sent invitation)
            const getInvitedNameFromCookie = () => {
              try {
                const stored = Cookies.get("invitedUserNames");
                if (stored) {
                  const names = JSON.parse(stored);
                  return names[formData.email] || formData.email.split("@")[0];
                }
              } catch (error) {
                console.warn("Failed to get invited name from cookie:", error);
              }
              return formData.email.split("@")[0];
            };

            // Handle invitation acceptance with original name from admin
            const acceptData = {
              token: invitationToken,
              name: getInvitedNameFromCookie(), // Use the name admin entered when sending invitation
              email: formData.email, // Include the invited user's email
              password: formData.password,
              confirmPassword: formData.confirmPassword,
              companyName: "User Company", // Default company name
            };
            console.log("Accepting invitation with data:", acceptData);
            const acceptResponse = await acceptInvitation(acceptData);

            if (acceptResponse.success) {
              console.log("Invitation accepted successfully, now signing in automatically...");

              // Clear invitation data
              setInvitationData(null);
              setInvitationToken(null);

              // Automatically sign in the user with their new credentials
              const loginData = {
                email: formData.email,
                password: formData.password,
              };

              const loginResponse = await authServices.signIn(loginData);
              console.log("Auto sign-in response after invitation:", JSON.stringify(loginResponse, null, 2));

              const authToken = loginResponse.authToken || loginResponse.token || loginResponse.accessToken;
              const refreshToken = loginResponse.refreshToken || loginResponse.refresh_token;

              console.log("Extracted tokens from invitation login:", { authToken, refreshToken });

              if (authToken && refreshToken) {
                console.log("Setting cookies with tokens after invitation");
                Cookies.set("authToken", authToken, {
                  expires: 1 / 24,
                  secure: process.env.NODE_ENV === "production",
                  sameSite: "strict",
                });
                Cookies.set("refreshToken", refreshToken, {
                  expires: 7,
                  secure: process.env.NODE_ENV === "production",
                  sameSite: "strict",
                });
                setToken(authToken);
              } else {
                console.warn("Login response missing tokens after invitation. Full response:", loginResponse);
              }

              const userId = loginResponse.result?.id || loginResponse.user?.id || loginResponse.id;
              const userRole = loginResponse.result?.role || loginResponse.user?.role || loginResponse.role;
              console.log("Extracted user ID after invitation:", userId);
              console.log("Extracted user role after invitation:", userRole);
              setUserId(userId);
              setUserRole(userRole);
              setIsAuthenticated(true);
              console.log("Authentication state set to true after invitation acceptance");
            } else {
              throw new Error(acceptResponse.error || "Failed to accept invitation");
            }
          } else {
            // Regular signup flow
            const signUpData = {
              name: formData.name || formData.email.split("@")[0],
              email: formData.email,
              password: formData.password,
              confirmPassword: formData.confirmPassword,
              role: "admin",
            };
            console.log("Sending sign-up request with data:", signUpData);
            const signUpResponse = await authServices.signUp(signUpData);
            console.log("Sign-up API response:", JSON.stringify(signUpResponse, null, 2));

            if (signUpResponse.success) {
              console.log("Sign-up successful, now signing in automatically...");

              const loginData = {
                email: formData.email,
                password: formData.password,
              };

              const loginResponse = await authServices.signIn(loginData);
              console.log("Auto sign-in response:", JSON.stringify(loginResponse, null, 2));

              const authToken = loginResponse.authToken || loginResponse.token || loginResponse.accessToken;
              const refreshToken = loginResponse.refreshToken || loginResponse.refresh_token;

              console.log("Extracted tokens from login:", { authToken, refreshToken });

              if (authToken && refreshToken) {
                console.log("Setting cookies with tokens");
                Cookies.set("authToken", authToken, {
                  expires: 1 / 24,
                  secure: process.env.NODE_ENV === "production",
                  sameSite: "strict",
                });
                Cookies.set("refreshToken", refreshToken, {
                  expires: 7,
                  secure: process.env.NODE_ENV === "production",
                  sameSite: "strict",
                });
                setToken(authToken);
              } else {
                console.warn("Login response missing tokens. Full response:", loginResponse);
              }

              const userId = loginResponse.result?.id || loginResponse.user?.id || loginResponse.id || signUpResponse.result?.id;
              const userRole = loginResponse.result?.role || loginResponse.user?.role || loginResponse.role;
              console.log("Extracted user ID:", userId);
              console.log("Extracted user role:", userRole);
              setUserId(userId);
              setUserRole(userRole);
              setIsAuthenticated(true);
              console.log("Authentication state set to true");
            } else {
              throw new Error(signUpResponse.message || "Sign-up failed");
            }
          }
        } else if (activeTab === "Sign In") {
          const loginData = {
            email: formData.email,
            password: formData.password,
          };
          const data = await authServices.signIn(loginData);
          console.log("Sign-in successful:", data);
          const userRole = data.result?.role || data.user?.role || data.role;
          console.log("Extracted user role from sign-in:", userRole);
          setToken(data.refreshToken);
          setUserId(data.id);
          setUserRole(userRole);
          setIsAuthenticated(true);
        }
        setSubmitAttempted(false);
        return true;
      } catch (error: unknown) {
        const err = error as {
          response?: {
            data?: { message?: string; errors?: string[] };
            status?: number;
          };
          message?: string;
        };
        console.error("API error:", err.response?.data || err.message);
        const errorData = err.response?.data || {
          message: err.message,
          errors: [],
        };
        const errorMsg = errorData.message || "An error occurred";
        const specificErrors = Array.isArray(errorData.errors)
          ? errorData.errors
          : [];
        showDetailedErrorToast(errorMsg, specificErrors);
        setErrors((prev) => ({
          ...prev,
          email: "",
          password: "",
        }));
        return false;
      }
    },
    [
      activeTab,
      formData,
      setErrors,
      setSubmitAttempted,
      validateForm,
      setIsAuthenticated,
      setToken,
      setUserId,
      setUserRole,
      invitationData,
      invitationToken,
      setInvitationData,
    ]
  );

  return {
    handleChange,
    handleFocus,
    handleBlur,
    handleSubmit,
  };
};

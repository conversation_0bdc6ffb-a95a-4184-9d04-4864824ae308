import { useRouter } from "next/navigation";
import { useAuthStore } from "@/store/authStore";
import { authServices } from "@/utils/authServices";
import { showDetailedErrorToast } from "@/components/ErrorToast";

export const useResetPassword = () => {
  const router = useRouter();
  const {
    formData,
    setFormData,
    errors,
    setErrors,
    showPassword,
    setShowPassword,
    showConfirmPassword,
    setShowConfirmPassword,
    otp,
  } = useAuthStore();

  const password = formData.password;
  const confirmPassword = formData.confirmPassword;
  const email = formData.email;
  const passwordError = errors.password;
  const confirmPasswordError = errors.confirmPassword;

  const isPasswordValid = () => password.length >= 8;

  const isConfirmPasswordValid = () =>
    confirmPassword === password && confirmPassword.length > 0;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    let hasErrors = false;

    setErrors((prevErrors) => {
      const updatedErrors = { ...prevErrors };

      if (!password) {
        updatedErrors.password = "Password is required";
        hasErrors = true;
      } else if (!isPasswordValid()) {
        updatedErrors.password = "Password must be at least 8 characters long";
        hasErrors = true;
      } else {
        updatedErrors.password = "";
      }

      if (!confirmPassword) {
        updatedErrors.confirmPassword = "Confirm Password is required";
        hasErrors = true;
      } else if (!isConfirmPasswordValid()) {
        updatedErrors.confirmPassword = "Passwords do not match";
        hasErrors = true;
      } else {
        updatedErrors.confirmPassword = "";
      }

      return updatedErrors;
    });

    if (!hasErrors) {
      try {
        const resetData = {
          email: email,
          otp: otp.join(""),
          newPassword: password,
          confirmPassword: confirmPassword,
        };
        const data = await authServices.resetPassword(resetData);
        console.log("Password reset successful:", data);
        setFormData({ email: "", password: "", confirmPassword: "" });
        return true;
      } catch (error: unknown) {
        const err = error as {
          response?: { data?: { message?: string; errors?: string[] } };
          message?: string;
        };
        console.error(
          "Error resetting password:",
          err.response?.data || err.message
        );
        const errorData = err.response?.data || { message: err.message, errors: [] };
        const errorMsg = errorData.message || "Failed to reset password";
        const specificErrors = Array.isArray(errorData.errors) ? errorData.errors : [];
        showDetailedErrorToast(errorMsg, specificErrors);
        setErrors((prev) => ({
          ...prev,
          password: "",
        }));
        return false;
      }
    }
    return false;
  };

  const handleGoBack = () => {
    router.push("/auth");
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  return {
    password,
    setPassword: (value: string) => {
      setFormData({ password: value });
      setErrors((prevErrors) => ({
        ...prevErrors,
        password: !value
          ? "Password is required"
          : !isPasswordValid()
          ? "Password must be at least 8 characters long"
          : "",
      }));
    },
    confirmPassword,
    setConfirmPassword: (value: string) => {
      setFormData({ confirmPassword: value });
      setErrors((prevErrors) => ({
        ...prevErrors,
        confirmPassword: !value
          ? "Confirm Password is required"
          : !isConfirmPasswordValid()
          ? "Passwords do not match"
          : "",
      }));
    },
    passwordError,
    confirmPasswordError,
    isPasswordValid: isPasswordValid(),
    isConfirmPasswordValid: isConfirmPasswordValid(),
    isPasswordVisible: showPassword,
    isConfirmPasswordVisible: showConfirmPassword,
    handleSubmit,
    handleGoBack,
    togglePasswordVisibility,
    toggleConfirmPasswordVisibility,
  };
};
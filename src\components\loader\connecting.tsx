import React, { useEffect, useState, useRef } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";

export default function Connecting({
  route,
  destination,
}: {
  route?: boolean;
  destination?: string;
}) {
  const [progress, setProgress] = useState(0);
  const [currentText, setCurrentText] = useState("Smart Monitoring...");
  const router = useRouter();
  const intervalId = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const duration = 4000;
    const updateInterval = 40;
    const totalSteps = Math.ceil(duration / updateInterval);
    let currentStep = 0;

    intervalId.current = setInterval(() => {
      currentStep++;
      const currentProgress = Math.min((currentStep / totalSteps) * 100, 100);
      setProgress(currentProgress);
      console.log(
        `Progress: ${currentProgress.toFixed(
          2
        )}%, Step: ${currentStep}/${totalSteps}`
      );

      if (currentProgress >= 100) {
        clearInterval(intervalId.current!);
        if (route && destination) {
          router.push(destination);
        }
      }
    }, updateInterval);

    const textInterval = setInterval(() => {
      const texts = [
        "Smart Monitoring...",
        "Uncompromised Security",
        "Seamless Integration",
      ];
      setCurrentText((prevText) => {
        const currentIndex = texts.indexOf(prevText);
        const nextIndex = (currentIndex + 1) % texts.length;
        return texts[nextIndex];
      });
    }, 1333);

    return () => {
      if (intervalId.current) {
        clearInterval(intervalId.current);
      }
      clearInterval(textInterval);
    };
  }, [router, route, destination]);

  return (
    <div className="w-full relative">
      <div className="w-full h-[10px] bg-[#1F1F1F] mb-2 rounded">
        <div
          className="h-full bg-[#3D3D3D] transition-[width] duration-[40ms] ease-linear"
          style={{ width: `${progress}%` }}
        ></div>
      </div>
      <div
        className="absolute transition-[left] duration-[40ms] ease-linear"
        style={{
          left: `${progress}%`,
          top: "-10px",
          transform: "translateX(-50%)",
        }}
      >
        <Image
          src="/loader-indicator.svg"
          alt="Loading indicator"
          width={26}
          height={23}
          priority
          style={{ width: "26px", height: "23px" }}
        />
      </div>

      <p className="text-sm text-center pt-5 text-[#E4E7EC]">{currentText}</p>
    </div>
  );
}

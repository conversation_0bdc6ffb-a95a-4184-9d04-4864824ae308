"use client";
import React, { useState } from "react";
import { AuthInputField } from "@/components/InputField";
import { SubmitButton, BlurredButton } from "@/components/button";
import Image from "next/image";
import { useResetPassword } from "./hooks";
import Loading from "@/components/loader/loading";

export default function ResetPassword() {
  const {
    password,
    setPassword,
    confirmPassword,
    setConfirmPassword,
    passwordError,
    confirmPasswordError,
    isPasswordVisible,
    isConfirmPasswordVisible,
    handleSubmit,
    handleGoBack,
    togglePasswordVisibility,
    toggleConfirmPasswordVisibility,
  } = useResetPassword();
  const [showLoading, setShowLoading] = useState(false);

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const success = await handleSubmit(e);
    if (success) {
      setShowLoading(true);
    }
  };

  return (
    <div className="min-h-screen text-white 2xl:p-20 p-14 relative">
      {showLoading && (
        <div className="absolute inset-0 z-50">
          <Loading route={true} destination="/auth" />
        </div>
      )}

      <div className="">
        <BlurredButton onClick={handleGoBack}>Go back to Sign in</BlurredButton>
      </div>

      <div className="flex flex-col items-center justify-center flex-1 2xl:mt-20 mt-14">
        <div className="2xl:mb-20 mb-12 flex items-center gap-4">
          <Image
            src="/teletraan.svg"
            alt="Teletraan Logo"
            width={70}
            height={70}
            className="2xl:w-[70px] w-[60px]"
          />
          <h1 className="text-4xl teletraan-title 2xl:text-5xl font-light tracking-wider">
            TELETRAAN
          </h1>
        </div>

        <div className="2xl:py-16 py-8 2xl:px-16 px-10 rounded-4xl shadow-lg relative w-full 2xl:max-w-2xl max-w-xl mx-4 border border-solid gradient-border">
          <h2 className="2xl:text-4xl text-2xl font-medium text-center mb-6">
            Reset Password
          </h2>
          <form onSubmit={handleFormSubmit} className="space-y-4">
            <AuthInputField
              id="resetPassword"
              label="Password"
              type="password"
              placeholder="Enter Password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              onFocus={() => {}}
              onBlur={() => {}}
              error={passwordError}
              isFocused={true}
              showToggle={true}
              isVisible={isPasswordVisible}
              toggleVisibility={togglePasswordVisibility}
            />
            <AuthInputField
              id="confirmPassword"
              label="Confirm Password"
              type="password"
              placeholder="Enter Password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              onFocus={() => {}}
              onBlur={() => {}}
              error={confirmPasswordError}
              isFocused={true}
              showToggle={true}
              isVisible={isConfirmPasswordVisible}
              toggleVisibility={toggleConfirmPasswordVisibility}
            />
            <div className="my-5 text-center">
              <SubmitButton
                label="Set New Password"
                onClick={handleFormSubmit}
              />
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
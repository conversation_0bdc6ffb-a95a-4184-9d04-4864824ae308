"use client";
import React from "react";
import { IoChevronDown } from "react-icons/io5";

interface FilterDropdownProps {
  label: string;
  isOpen: boolean;
  onToggle: () => void;
  options?: string[];
  selectedValue?: string;
  onSelect?: (value: string) => void;
}

const FilterDropdown: React.FC<FilterDropdownProps> = ({
  label,
  isOpen,
  onToggle,
  options = [],
  selectedValue,
  onSelect,
}) => (
  <div className="relative">
    <button
      type="button"
      onClick={onToggle}
      className="flex cursor-pointer items-center justify-between px-4 py-4 text-[#BFBFBF] text-sm min-w-[140px] border border-[#A3A3A3]"
    >
      <span>{selectedValue || label}</span>
      <IoChevronDown
        size={16}
        className={`transition-transform ${isOpen ? "rotate-180" : ""}`}
      />
    </button>
    {isOpen && options.length > 0 && (
      <div className="absolute top-full left-0 right-0 bg-[#2E2E2E] border border-[#3D3D3D] z-10 max-h-48 overflow-y-auto">
        <div
          className="px-4 py-3 cursor-pointer text-sm text-[#E4E7EC] hover:bg-[#3D3D3D] transition-colors"
          onClick={() => {
            onSelect?.("");
            onToggle();
          }}
        >
          All
        </div>
        {options.map((option) => (
          <div
            key={option}
            className={`px-4 py-3 cursor-pointer text-sm transition-colors ${
              selectedValue === option 
                ? "bg-[#3D3D3D] text-white" 
                : "text-[#E4E7EC] hover:bg-[#3D3D3D]"
            }`}
            onClick={() => {
              onSelect?.(option);
              onToggle();
            }}
          >
            {option}
          </div>
        ))}
      </div>
    )}
  </div>
);

export default FilterDropdown;

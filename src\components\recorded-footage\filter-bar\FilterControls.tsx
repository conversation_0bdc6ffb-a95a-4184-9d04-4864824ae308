"use client";
import React from "react";
import FilterDropdown from "./FilterDropdown";
import { FilterControlsProps } from "@/types/filterTypes";
import { TIME_RANGE_OPTIONS, FACE_RECOGNITION_OPTIONS } from "./constants";

const FilterControls: React.FC<FilterControlsProps> = ({
  selectedTimeRange,
  setSelectedTimeRange,
  selectedFaceRecognition,
  setSelectedFaceRecognition,
  selectedDateRange,
  onDateRangeClick,
  selectedCustomTimeRange,
  setSelectedCustomTimeRange, // eslint-disable-line @typescript-eslint/no-unused-vars
  onTimeRangeClick,
}) => {
  const formatDateRangeLabel = () => {
    if (!selectedDateRange.startDate && !selectedDateRange.endDate) {
      return "Date Range";
    }
    if (selectedDateRange.startDate && selectedDateRange.endDate) {
      const start = selectedDateRange.startDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      const end = selectedDateRange.endDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });

      // If same year, don't repeat it
      if (selectedDateRange.startDate.getFullYear() === selectedDateRange.endDate.getFullYear()) {
        return `${start} - ${end}`;
      } else {
        const startWithYear = selectedDateRange.startDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: '2-digit' });
        const endWithYear = selectedDateRange.endDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: '2-digit' });
        return `${startWithYear} - ${endWithYear}`;
      }
    }
    if (selectedDateRange.startDate) {
      return `From ${selectedDateRange.startDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`;
    }
    return "Date Range";
  };

  const formatTimeRangeLabel = () => {
    if (selectedTimeRange === "Custom" && selectedCustomTimeRange.startTime && selectedCustomTimeRange.endTime) {
      return `${selectedCustomTimeRange.startTime} - ${selectedCustomTimeRange.endTime}`;
    }
    return selectedTimeRange || "Time Range";
  };

  return (
    <div className="lg:flex lg:items-center lg:space-x-4 mt-3 lg:mt-0">
      <div className="text-sm text-[#8A8A8A] mb-3 lg:mb-0">Filters:</div>
      <div className="lg:flex lg:space-x-4 space-x-0 space-y-3 lg:space-y-0 items-center flex-wrap gap-y-3">
        {/* Date Range - Regular button, not dropdown */}
        <button
          type="button"
          onClick={onDateRangeClick}
          className={`px-4 py-4 cursor-pointer text-xs min-w-[140px] border border-[#A3A3A3] transition-colors ${
            selectedDateRange.startDate || selectedDateRange.endDate
              ? "text-[#E4E7EC] bg-[#2E2E2E]"
              : "text-[#BFBFBF]"
          }`}
        >
          {formatDateRangeLabel()}
        </button>

        {/* Time Range - Dropdown */}
        <FilterDropdown
          label="Time Range"
          options={TIME_RANGE_OPTIONS}
          selectedOption={formatTimeRangeLabel()}
          onSelect={setSelectedTimeRange}
          onCustomClick={onTimeRangeClick}
        />

        {/* Face Recognition - Dropdown */}
        <FilterDropdown
          label="Face Recognition"
          options={FACE_RECOGNITION_OPTIONS}
          selectedOption={selectedFaceRecognition}
          onSelect={setSelectedFaceRecognition}
        />
      </div>
    </div>
  );
};

export default FilterControls;

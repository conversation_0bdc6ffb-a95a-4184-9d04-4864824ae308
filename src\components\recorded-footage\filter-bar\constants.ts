import { FilterTab } from "@/types/filterTypes";

export const FILTER_TABS: FilterTab[] = [
  { id: "all", label: "All" },
  { id: "camera", label: "Camera Name", iconSrc: "/camera-name.svg" },
  { id: "location", label: "Location", iconSrc: "/location.svg" },
  { id: "tags", label: "Tags", iconSrc: "/tags.svg" },
];

export const MOCK_CAMERA_OPTIONS = [
  "Front Gate Cam",
  "Office Main Entrance",
  "Cash Register Cam",
  "Elevator Cam 1",
  "Parking Lot Monitor",
  "Side Alley Watch"
];

export const MOCK_LOCATION_OPTIONS = [
  "Branch A-Parking Lot",
  "Factory site- Entrance",
  "Campus-Maingate",
  "Conference Room",
  "Bank- ATM Area",
  "Factory Site Entrance"
];

export const MOCK_TAG_OPTIONS = [
  "Unknown Face",
  "Known Face",
  "Multiple Persons",
  "Object Moved",
  "Gate Opened",
  "Door Opened",
  "Colour of Clothing",
  "Male",
  "Female"
];

export const TIME_RANGE_OPTIONS = [
  "Custom",
  "Today",
  "Yesterday",
  "Last Seven Days",
  "This Week",
  "Last 30 Days",
];

export const FACE_RECOGNITION_OPTIONS = [
  "Known Face",
  "Unknown Face",
  "Unrecognised Face",
];

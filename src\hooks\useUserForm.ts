import { useState, useCallback, useEffect } from "react";
import { UserFormData, UserFormErrors, User } from "@/types/userTypes";

export const useUserForm = (onSubmit: (userData: User) => Promise<void>, editingUser?: User | null) => {
  const isEditMode = !!editingUser;
  const [formData, setFormData] = useState<UserFormData>({
    name: "",
    email: "",
    role: "",
    organizationName: "",
  });

  const [errors, setErrors] = useState<UserFormErrors>({
    name: "",
    email: "",
    role: "",
    organizationName: "",
  });

  const [focusedField, setFocusedField] = useState<string | null>(null);
  const [submitAttempted, setSubmitAttempted] = useState(false);

  // Populate form when editing a user
  useEffect(() => {
    if (editingUser) {
      setFormData({
        name: editingUser.name,
        email: editingUser.email,
        role: editingUser.role,
        organizationName: "",
      });
      // Clear any previous errors
      setErrors({ name: "", email: "", role: "", organizationName: "" });
      setSubmitAttempted(false);
    } else {
      // Reset form for new user
      setFormData({
        name: "",
        email: "",
        role: "",
        organizationName: "",
      });
      setErrors({ name: "", email: "", role: "", organizationName: "" });
      setSubmitAttempted(false);
    }
  }, [editingUser]);

  const validateEmail = useCallback((email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }, []);

  const validateName = useCallback((name: string): boolean => {
    return name.trim().length > 0;
  }, []);

  const validateRole = useCallback((role: string): boolean => {
    return ["Admin", "User", "Security", "Audit"].includes(role);
  }, []);

  const validateOrganizationName = useCallback((organizationName: string): boolean => {
    return organizationName.trim().length > 0;
  }, []);

  const validateField = useCallback((field: keyof UserFormData, value: string) => {
    switch (field) {
      case "name":
        return validateName(value) ? "" : "Name is required";
      case "email":
        if (!value.trim()) return "Email is required";
        return validateEmail(value) ? "" : "Please enter a valid email address";
      case "role":
        return validateRole(value) ? "" : "Please select a role";
      case "organizationName":
        // Only require organization name for new users (invitations), not when editing
        if (isEditMode) return "";
        return validateOrganizationName(value) ? "" : "Organization name is required";
      default:
        return "";
    }
  }, [validateEmail, validateName, validateRole, validateOrganizationName, isEditMode]);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    const field = id as keyof UserFormData;
    
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Real-time validation
    const error = validateField(field, value);
    setErrors(prev => ({ ...prev, [field]: error }));
  }, [validateField]);

  const handleRoleChange = useCallback((role: string) => {
    setFormData(prev => ({ ...prev, role: role as UserFormData["role"] }));
    
    // Real-time validation for role
    const error = validateField("role", role);
    setErrors(prev => ({ ...prev, role: error }));
  }, [validateField]);

  const handleFocus = useCallback((field: string) => {
    setFocusedField(field);
  }, []);

  const handleBlur = useCallback(() => {
    setFocusedField(null);
  }, []);

  const isFormValid = useCallback(() => {
    const nameValid = validateName(formData.name);
    const emailValid = validateEmail(formData.email);
    const roleValid = validateRole(formData.role);
    // Only require organization name for new users (invitations), not when editing
    const organizationNameValid = isEditMode || validateOrganizationName(formData.organizationName);

    return nameValid && emailValid && roleValid && organizationNameValid;
  }, [formData, validateName, validateEmail, validateRole, validateOrganizationName, isEditMode]);

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitAttempted(true);

    // Validate all fields
    const newErrors: UserFormErrors = {
      name: validateField("name", formData.name),
      email: validateField("email", formData.email),
      role: validateField("role", formData.role),
      organizationName: validateField("organizationName", formData.organizationName),
    };

    setErrors(newErrors);

    // Check if form is valid
    if (Object.values(newErrors).every(error => error === "")) {
      // Create user object
      const userData: User = {
        id: editingUser ? editingUser.id : Date.now().toString(), // Preserve ID when editing
        name: formData.name.trim(),
        email: formData.email.trim(),
        role: formData.role as "Admin" | "User" | "Security" | "Audit",
        status: editingUser ? editingUser.status : "Pending", // New users are Pending by default
        dateAdded: editingUser ? editingUser.dateAdded : new Date().toLocaleDateString("en-US", {
          year: "numeric",
          month: "long",
          day: "numeric"
        }),
        organizationName: formData.organizationName.trim() || undefined,
      };

      await onSubmit(userData);

      // Don't reset form here - let the parent component handle it
    }
  }, [formData, validateField, onSubmit, editingUser]);

  const resetForm = useCallback(() => {
    setFormData({
      name: "",
      email: "",
      role: "",
      organizationName: "",
    });
    setErrors({
      name: "",
      email: "",
      role: "",
      organizationName: "",
    });
    setSubmitAttempted(false);
    setFocusedField(null);
  }, []);

  return {
    formData,
    errors,
    focusedField,
    submitAttempted,
    handleChange,
    handleRoleChange,
    handleFocus,
    handleBlur,
    handleSubmit,
    resetForm,
    setFormData,
    isFormValid: isFormValid(),
  };
};

"use client";
import React, { useState } from "react";
import { IoChevronDown } from "react-icons/io5";
import { FilterDropdownProps } from "@/types/filterTypes";

const FilterDropdown: React.FC<FilterDropdownProps> = ({
  label,
  options,
  selectedOption,
  onSelect,
  onCustomClick,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="relative">
      <div
        className="flex cursor-pointer items-center justify-between px-4 py-4 text-[#BFBFBF] text-xs min-w-[140px] border border-[#A3A3A3]"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span>
          {selectedOption && selectedOption !== "" ? selectedOption : label}
        </span>
        <IoChevronDown
          size={16}
          className={`ml-2 transition-transform ${isOpen ? "rotate-180" : ""}`}
        />
      </div>
      {isOpen && (
        <div className="absolute top-full left-0 right-0 bg-[#2E2E2E] border border-[#3D3D3D] z-10 max-h-48 overflow-y-auto">
          {options.map((option) => (
            <div
              key={option}
              className="px-4 py-3 cursor-pointer text-xs text-[#BFBFBF]"
              onClick={() => {
                if (option === "Custom" && onCustomClick) {
                  onCustomClick();
                } else {
                  onSelect(option);
                }
                setIsOpen(false);
              }}
            >
              {option}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default FilterDropdown;

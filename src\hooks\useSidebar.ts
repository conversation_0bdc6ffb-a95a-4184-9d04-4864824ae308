import { useCallback } from "react";
import { useDashboardStore } from "@/store/dashboardStore";

export function useSidebar() {
  const { isSidebarOpen, setIsSidebarOpen } = useDashboardStore();

  const toggleSidebar = useCallback(() => {
    setIsSidebarOpen(!isSidebarOpen);
  }, [isSidebarOpen, setIsSidebarOpen]);

  const closeSidebar = useCallback(() => {
    if (isSidebarOpen) setIsSidebarOpen(false);
  }, [isSidebarOpen, setIsSidebarOpen]);

  return { isSidebarOpen, toggleSidebar, closeSidebar };
}

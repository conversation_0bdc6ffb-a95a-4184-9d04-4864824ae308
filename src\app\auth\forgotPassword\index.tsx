import React, { useEffect } from "react";
import { Modal } from "@/components/Modal";
import { AuthInputField } from "@/components/InputField";
import { SubmitButton } from "@/components/button";
import Image from "next/image";
import { useForgotPassword } from "./hooks";

export const ForgotPasswordModal = ({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) => {
  const {
    step,
    email,
    setEmail,
    emailError,
    otp,
    otpSent,
    otpError,
    handleSendOTP,
    handleOtpChange,
    handleOtpSubmit,
    resetModal,
    setStep,
    setOtpSent,
  } = useForgotPassword();

  useEffect(() => {
    if (otpSent) {
      const timer = setTimeout(() => {
        setOtpSent(false);
        setStep(2);
      }, 500);
      return () => clearTimeout(timer);
    }
    if (step === 3) {
      console.log("OTP verified:", otp.join(""));
      resetModal(onClose);
    }
  }, [otpSent, step, otp, onClose, setOtpSent, setStep, resetModal]);

  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData("text").trim();
    if (pastedData.length >= 5) {
      handleOtpChange(0, pastedData, true);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={() => resetModal(onClose)}>
      {step === 1 && (
        <div className="text-center">
          <h2 className="text-2xl font-medium text-[#F2F4F7] my-6">
            Forgot Password?
          </h2>
          <div className="text-left my-4">
            <AuthInputField
              id="forgotEmail"
              label="Email"
              type="email"
              placeholder="Enter Email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              onFocus={() => {}}
              onBlur={() => {}}
              error={emailError}
              isFocused={true}
              submitAttempted={!!emailError}
            />
          </div>
          {otpSent ? (
            <div className="flex justify-center mt-6 mb-8">
              <div className="w-48 bg-[#3D3D3D] text-white cursor-pointer font-medium border border-[#242424] py-3 mt-6 transition-colors flex items-center justify-center gap-2">
                <Image
                  src="/tick.svg"
                  alt="Tick icon"
                  width={20}
                  height={20}
                  className="object-contain"
                />
                <span className="text-base">OTP Sent</span>
              </div>
            </div>
          ) : (
            <div className="flex justify-center mt-6 mb-8">
              <SubmitButton label="Send OTP" onClick={handleSendOTP} />
            </div>
          )}
        </div>
      )}
      {(step === 2 || step === 3) && (
        <div className="text-center">
          <h2 className="text-2xl font-medium text-[#F2F4F7] my-6">
            Enter One Time Code
          </h2>
          <div className="flex justify-center gap-4 mb-4 mt-10">
            {otp.map((digit, index) => (
              <input
                title="OTP Inputs"
                key={index}
                id={`otp-${index}`}
                type="text"
                inputMode="numeric"
                pattern="[0-9]*"
                value={digit}
                onChange={(e) => handleOtpChange(index, e.target.value)}
                onPaste={handlePaste}
                className="w-20 h-20 bg-[#2E2E2E] text-center text-white text-2xl focus:outline-none focus:ring-1 focus:ring-[#E4E7EC]"
                maxLength={1}
                autoFocus={index === 0}
              />
            ))}
          </div>
          {otpError && <p className="text-red-500 text-sm mt-2">{otpError}</p>}
          <div className="mb-8">
            <SubmitButton label="Next" onClick={handleOtpSubmit} />
          </div>
        </div>
      )}
    </Modal>
  );
};

export default ForgotPasswordModal;
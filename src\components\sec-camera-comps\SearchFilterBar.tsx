import React, { useState } from "react";
import { IoChevronDown } from "react-icons/io5";
import { CiSearch } from "react-icons/ci";
import { AiOutlinePlus } from "react-icons/ai";

interface FilterDropdownProps {
  label: string;
  options: string[];
  selectedOption: string;
  onSelect: (option: string) => void;
}

const FilterDropdown: React.FC<FilterDropdownProps> = ({
  label,
  options,
  selectedOption,
  onSelect,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleDropdown = () => setIsOpen(!isOpen);

  const handleSelect = (option: string) => {
    onSelect(option === "All Cameras" ? "" : option);
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <div
        className="flex items-center border border-[#A3A3A3] px-4 py-3 cursor-pointer"
        onClick={toggleDropdown}
      >
        <span className="text-[#E4E7EC] text-xs mr-2">
          {selectedOption || label}
        </span>
        <IoChevronDown className="text-[#E4E7EC]" />
      </div>
      {isOpen && (
        <div className="absolute z-10 w-full bg-[#1F1F1F] border border-[#A3A3A3] mt-1 rounded shadow-lg">
          {options.map((option) => (
            <div
              key={option}
              className="px-4 py-2 text-[#E4E7EC] text-xs cursor-pointer"
              onClick={() => handleSelect(option)}
            >
              {option}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

interface SearchFilterBarProps {
  onAddCamera: () => void;
  locations: string[];
  cameraIds: string[];
  selectedLocation: string;
  setSelectedLocation: (location: string) => void;
  selectedCameraId: string;
  setSelectedCameraId: (id: string) => void;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
}

const SearchFilterBar: React.FC<SearchFilterBarProps> = ({
  onAddCamera,
  locations,
  cameraIds,
  selectedLocation,
  setSelectedLocation,
  selectedCameraId,
  setSelectedCameraId,
  searchTerm,
  setSearchTerm,
}) => {
  return (
    <div className="lg:flex items-center justify-between mb-6">
      <div className="flex items-center bg-[#1F1F1F] px-3 py-2 lg:w-1/4">
        <CiSearch size={26} className="text-[#8A8A8A] mr-2" />
        <input
          type="text"
          placeholder="Search Location/Camera"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="bg-transparent text-white placeholder:text-sm placeholder-[#8A8A8A] focus:outline-none w-full"
        />
      </div>
      <div className="lg:flex lg:space-x-3 space-x-0 space-y-3 lg:space-y-0 mt-3 lg:mt-0  items-center">
        <FilterDropdown
          label="All Locations"
          options={locations}
          selectedOption={selectedLocation}
          onSelect={setSelectedLocation}
        />
        <FilterDropdown
          label="All Cameras"
          options={cameraIds}
          selectedOption={selectedCameraId || "All Cameras"}
          onSelect={setSelectedCameraId}
        />
        <div>
          <div
            className="flex cursor-pointer items-center px-5 py-2 bg-[#3D3D3D] text-[#BFBFBF] text-xs"
            onClick={onAddCamera}
          >
            <AiOutlinePlus size={20} className="mr-2" />
            Add Camera
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchFilterBar;
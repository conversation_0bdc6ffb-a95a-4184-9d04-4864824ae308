"use client";
import React, { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { SubmitButton } from "@/components/button";
import { TimeRangeModalProps } from "@/types/filterTypes";

const TimeRangeModal: React.FC<TimeRangeModalProps> = ({
  isOpen,
  onClose,
  selectedTimeRange,
  onTimeRangeSelect,
}) => {
  // Note: startTime and endTime state variables removed as they were unused
  const [startHour, setStartHour] = useState<string>("");
  const [startMinute, setStartMinute] = useState<string>("");
  const [startSecond, setStartSecond] = useState<string>("");
  const [startPeriod, setStartPeriod] = useState<"AM" | "PM">("AM");
  const [endHour, setEndHour] = useState<string>("");
  const [endMinute, setEndMinute] = useState<string>("");
  const [endSecond, setEndSecond] = useState<string>("");
  const [endPeriod, setEndPeriod] = useState<"AM" | "PM">("AM");
  const [errors, setErrors] = useState<{ start?: string; end?: string }>({});

  // Refs for focus management
  const startHourRef = useRef<HTMLInputElement | null>(null);
  const startMinuteRef = useRef<HTMLInputElement | null>(null);
  const startSecondRef = useRef<HTMLInputElement | null>(null);
  const endHourRef = useRef<HTMLInputElement | null>(null);
  const endMinuteRef = useRef<HTMLInputElement | null>(null);
  const endSecondRef = useRef<HTMLInputElement | null>(null);

  useEffect(() => {
    if (isOpen) {
      if (selectedTimeRange.startTime) {
        parseTimeString(selectedTimeRange.startTime, "start");
      } else {
        resetStartTime();
      }
      if (selectedTimeRange.endTime) {
        parseTimeString(selectedTimeRange.endTime, "end");
      } else {
        resetEndTime();
      }
      setErrors({});
    }
  }, [isOpen, selectedTimeRange]);

  const parseTimeString = (timeString: string, type: "start" | "end") => {
    const [time, period] = timeString.split(" ");
    const [hour, minute, second] = time.split(":");
    
    if (type === "start") {
      setStartHour(hour);
      setStartMinute(minute);
      setStartSecond(second || "00");
      setStartPeriod(period as "AM" | "PM");
    } else {
      setEndHour(hour);
      setEndMinute(minute);
      setEndSecond(second || "00");
      setEndPeriod(period as "AM" | "PM");
    }
  };

  const resetStartTime = () => {
    setStartHour("");
    setStartMinute("");
    setStartSecond("");
    setStartPeriod("AM");
  };

  const resetEndTime = () => {
    setEndHour("");
    setEndMinute("");
    setEndSecond("");
    setEndPeriod("AM");
  };

  // Note: validateTimeInput function removed as it was unused

  const formatTimeComponent = (value: string): string => {
    if (value === "") return "";
    return value.padStart(2, "0");
  };



  const constructTimeString = (hour: string, minute: string, second: string, period: "AM" | "PM"): string | null => {
    if (!hour || !minute || !second) return null;

    const hourNum = parseInt(hour);
    const minuteNum = parseInt(minute);
    const secondNum = parseInt(second);

    // Validate ranges
    if (isNaN(hourNum) || hourNum < 1 || hourNum > 12) return null;
    if (isNaN(minuteNum) || minuteNum < 0 || minuteNum > 59) return null;
    if (isNaN(secondNum) || secondNum < 0 || secondNum > 59) return null;

    // Format with leading zeros for display
    const formattedHour = formatTimeComponent(hour);
    const formattedMinute = formatTimeComponent(minute);
    const formattedSecond = formatTimeComponent(second);

    return `${formattedHour}:${formattedMinute}:${formattedSecond} ${period}`;
  };

  const validateTimes = (): boolean => {
    const newErrors: { start?: string; end?: string } = {};

    // Validate individual fields
    const startHourNum = parseInt(startHour);
    const startMinuteNum = parseInt(startMinute);
    const startSecondNum = parseInt(startSecond);
    const endHourNum = parseInt(endHour);
    const endMinuteNum = parseInt(endMinute);
    const endSecondNum = parseInt(endSecond);

    if (!startHour || isNaN(startHourNum) || startHourNum < 1 || startHourNum > 12) {
      newErrors.start = "Please enter a valid hour (1-12)";
    } else if (!startMinute || isNaN(startMinuteNum) || startMinuteNum < 0 || startMinuteNum > 59) {
      newErrors.start = "Please enter a valid minute (0-59)";
    } else if (!startSecond || isNaN(startSecondNum) || startSecondNum < 0 || startSecondNum > 59) {
      newErrors.start = "Please enter a valid second (0-59)";
    }

    if (!endHour || isNaN(endHourNum) || endHourNum < 1 || endHourNum > 12) {
      newErrors.end = "Please enter a valid hour (1-12)";
    } else if (!endMinute || isNaN(endMinuteNum) || endMinuteNum < 0 || endMinuteNum > 59) {
      newErrors.end = "Please enter a valid minute (0-59)";
    } else if (!endSecond || isNaN(endSecondNum) || endSecondNum < 0 || endSecondNum > 59) {
      newErrors.end = "Please enter a valid second (0-59)";
    }

    // If individual fields are valid, check time range
    if (Object.keys(newErrors).length === 0) {
      const startTimeString = constructTimeString(startHour, startMinute, startSecond, startPeriod);
      const endTimeString = constructTimeString(endHour, endMinute, endSecond, endPeriod);

      if (startTimeString && endTimeString) {
        const startDate = new Date(`2000-01-01 ${startTimeString}`);
        const endDate = new Date(`2000-01-01 ${endTimeString}`);

        if (startDate >= endDate) {
          newErrors.end = "End time must be after start time";
        }
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleApply = () => {
    if (validateTimes()) {
      const startTimeString = constructTimeString(startHour, startMinute, startSecond, startPeriod);
      const endTimeString = constructTimeString(endHour, endMinute, endSecond, endPeriod);
      
      onTimeRangeSelect({
        startTime: startTimeString,
        endTime: endTimeString,
      });
      onClose();
    }
  };

  const handleClear = () => {
    resetStartTime();
    resetEndTime();
    setErrors({});
  };

  const formatTimeRange = () => {
    const startTimeString = constructTimeString(startHour, startMinute, startSecond, startPeriod);
    const endTimeString = constructTimeString(endHour, endMinute, endSecond, endPeriod);
    
    if (!startTimeString && !endTimeString) return "Select time range";
    if (startTimeString && !endTimeString) return `${startTimeString} - Select end time`;
    if (startTimeString && endTimeString) {
      return `${startTimeString} - ${endTimeString}`;
    }
    return "Select time range";
  };

  const TimeInput = ({
    value,
    onChange,
    placeholder,
    min,
    max,
    nextFieldRef,
    inputRef
  }: {
    value: string;
    onChange: (value: string) => void;
    placeholder: string;
    min: number;
    max: number;
    nextFieldRef?: React.RefObject<HTMLInputElement | null> | null;
    inputRef?: React.RefObject<HTMLInputElement | null>;
  }) => {
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;
      const numericValue = newValue.replace(/\D/g, "");

      // Don't allow more than 2 digits
      if (numericValue.length > 2) {
        e.preventDefault();
        return;
      }

      // Call the onChange to update the parent state
      onChange(numericValue);

      // Auto-advance when exactly 2 digits are entered AND value is valid
      if (numericValue.length === 2) {
        const num = parseInt(numericValue);
        if (num >= min && num <= max && nextFieldRef?.current) {
          setTimeout(() => {
            if (nextFieldRef?.current) {
              nextFieldRef.current.focus();
            }
          }, 0);
        }
      }
    };

    return (
      <input
        ref={inputRef}
        type="text"
        value={value}
        onChange={handleInputChange}
        onKeyDown={(e) => {
          // Allow all navigation and editing keys
          if (['Backspace', 'Delete', 'Tab', 'ArrowLeft', 'ArrowRight', 'Home', 'End'].includes(e.key)) {
            return;
          }
          // Only allow numeric input for other keys
          if (!/[0-9]/.test(e.key)) {
            e.preventDefault();
          }
        }}
        placeholder={placeholder}
        className="w-full bg-[#2E2E2E] text-[#E4E7EC] text-center h-[72px] px-2 border border-[#3D3D3D] focus:outline-none focus:border-[#8A8A8A] text-sm rounded"
        maxLength={2}
      />
    );
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 bg-transparent flex items-center justify-center backdrop-blur-[6px] z-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          onClick={onClose}
        >
          <motion.div
            className="bg-[#1F1F1F] gradient-border p-6 shadow-lg relative w-full max-w-md mx-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="border-b border-[#3D3D3D] pb-4 mb-6">
              <h3 className="text-[#E4E7EC] text-lg font-medium text-center">
                Enter Time
              </h3>
            </div>

            {/* Start Time Section */}
            <div className="mb-6">
              <p className="text-[#8A8A8A] text-sm mb-3">Start Time</p>
              <div className="grid grid-cols-4 gap-3 items-center time-input-container">
                <div>
                  <p className="text-[#8A8A8A] text-xs mb-1">Hour</p>
                  <TimeInput
                    value={startHour}
                    onChange={setStartHour}
                    placeholder="12"
                    min={1}
                    max={12}
                    nextFieldRef={startMinuteRef}
                    inputRef={startHourRef}
                  />
                </div>
                <div>
                  <p className="text-[#8A8A8A] text-xs mb-1">Minute</p>
                  <TimeInput
                    value={startMinute}
                    onChange={setStartMinute}
                    placeholder="00"
                    min={0}
                    max={59}
                    nextFieldRef={startSecondRef}
                    inputRef={startMinuteRef}
                  />
                </div>
                <div>
                  <p className="text-[#8A8A8A] text-xs mb-1">Second</p>
                  <TimeInput
                    value={startSecond}
                    onChange={setStartSecond}
                    placeholder="00"
                    min={0}
                    max={59}
                    nextFieldRef={endHourRef}
                    inputRef={startSecondRef}
                  />
                </div>
                <div>
                  <p className="text-[#8A8A8A] text-xs mb-1">Period</p>
                  <div className="flex flex-col gap-1 h-[72px]">
                    <button
                      type="button"
                      onClick={() => setStartPeriod("AM")}
                      className={`flex-1 px-2 text-xs transition-colors rounded ${
                        startPeriod === "AM"
                          ? "bg-[#E4E7EC] text-[#1F1F1F]"
                          : "bg-[#2E2E2E] text-[#BFBFBF] border border-[#3D3D3D]"
                      }`}
                    >
                      AM
                    </button>
                    <button
                      type="button"
                      onClick={() => setStartPeriod("PM")}
                      className={`flex-1 px-2 text-xs transition-colors rounded ${
                        startPeriod === "PM"
                          ? "bg-[#E4E7EC] text-[#1F1F1F]"
                          : "bg-[#2E2E2E] text-[#BFBFBF] border border-[#3D3D3D]"
                      }`}
                    >
                      PM
                    </button>
                  </div>
                </div>
              </div>
              {errors.start && (
                <p className="text-red-500 text-xs mt-2">{errors.start}</p>
              )}
            </div>

            {/* End Time Section */}
            <div className="mb-6">
              <p className="text-[#8A8A8A] text-sm mb-3">End Time</p>
              <div className="grid grid-cols-4 gap-3 items-center time-input-container">
                <div>
                  <p className="text-[#8A8A8A] text-xs mb-1">Hour</p>
                  <TimeInput
                    value={endHour}
                    onChange={setEndHour}
                    placeholder="12"
                    min={1}
                    max={12}
                    nextFieldRef={endMinuteRef}
                    inputRef={endHourRef}
                  />
                </div>
                <div>
                  <p className="text-[#8A8A8A] text-xs mb-1">Minute</p>
                  <TimeInput
                    value={endMinute}
                    onChange={setEndMinute}
                    placeholder="00"
                    min={0}
                    max={59}
                    nextFieldRef={endSecondRef}
                    inputRef={endMinuteRef}
                  />
                </div>
                <div>
                  <p className="text-[#8A8A8A] text-xs mb-1">Second</p>
                  <TimeInput
                    value={endSecond}
                    onChange={setEndSecond}
                    placeholder="00"
                    min={0}
                    max={59}
                    inputRef={endSecondRef}
                  />
                </div>
                <div>
                  <p className="text-[#8A8A8A] text-xs mb-1">Period</p>
                  <div className="flex flex-col gap-1 h-[72px]">
                    <button
                      type="button"
                      onClick={() => setEndPeriod("AM")}
                      className={`flex-1 px-2 text-xs transition-colors rounded ${
                        endPeriod === "AM"
                          ? "bg-[#E4E7EC] text-[#1F1F1F]"
                          : "bg-[#2E2E2E] text-[#BFBFBF] border border-[#3D3D3D]"
                      }`}
                    >
                      AM
                    </button>
                    <button
                      type="button"
                      onClick={() => setEndPeriod("PM")}
                      className={`flex-1 px-2 text-xs transition-colors rounded ${
                        endPeriod === "PM"
                          ? "bg-[#E4E7EC] text-[#1F1F1F]"
                          : "bg-[#2E2E2E] text-[#BFBFBF] border border-[#3D3D3D]"
                      }`}
                    >
                      PM
                    </button>
                  </div>
                </div>
              </div>
              {errors.end && (
                <p className="text-red-500 text-xs mt-2">{errors.end}</p>
              )}
            </div>

            {/* Selected range display */}
            <div className="mb-6 p-3 bg-[#2E2E2E] rounded border border-[#3D3D3D]">
              <p className="text-[#8A8A8A] text-xs mb-1">Selected Range:</p>
              <p className="text-[#E4E7EC] text-sm">{formatTimeRange()}</p>
            </div>

            {/* Action buttons */}
            <div className="flex gap-3 items-end">
              <button
                type="button"
                onClick={handleClear}
                className="flex-1 px-4 py-3 text-[#BFBFBF] border border-[#3D3D3D] rounded hover:bg-[#2E2E2E] transition-colors"
              >
                Clear
              </button>
              <div className="flex-1 flex justify-center">
                <SubmitButton
                  label="Enter"
                  type="button"
                  onClick={handleApply}
                  disabled={!startHour || !startMinute || !startSecond || !endHour || !endMinute || !endSecond}
                />
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default TimeRangeModal;

"use client";
import React, { useEffect } from "react";
import Image from "next/image";

interface SuccessMessageProps {
  onComplete: () => void;
}

const CameraConnectedSuccess: React.FC<SuccessMessageProps> = ({ onComplete }) => {
  useEffect(() => {
    const timer = setTimeout(() => {
      onComplete();
    }, 2000);
    
    return () => clearTimeout(timer);
  }, [onComplete]);
  return (
    <div className="w-full flex flex-col items-center justify-center text-center py-6">
      <div className="mb-8">
        <Image
          src="/suc-con.svg"
          alt="Camera Connected"
          width={200}
          height={200}
          priority
        />
      </div>

      <h1 className="text-lg font-medium text-[#F2F4F7] mb-8">
        Camera Successfully Connected
      </h1>
    </div>
  );
};

export default CameraConnectedSuccess;

"use client";
import React from "react";
import Image from "next/image";
import ProgressIndicator from "@/components/loader/ProgressIndicator";

export default function Home() {
  return (
    <div className="relative flex flex-col items-center h-screen bg-[#121212] text-white p-4">
      <div className="absolute top-0 left-0 w-full h-[40%] overflow-hidden">
        <Image
          src="/bg1.svg"
          alt="Background Ellipse 1"
          layout="fill"
          objectFit="cover"
          className="opacity-99"
        />
      </div>

      <div className="absolute bottom-0 left-0 w-[25%] h-[50%] overflow-hidden">
        <Image
          src="/bg2.svg"
          alt="Background Ellipse 2"
          layout="fill"
          objectFit="cover"
          className="opacity-99"
        />
      </div>

      <div className="flex-1 flex flex-col justify-center items-center w-full max-w-lg mx-auto lg:mb-38 z-10">
        <h1 className="text-4xl teletraan-title md:text-5xl font-light mb-8 tracking-wider">
          TELETRAAN
        </h1>
        <ProgressIndicator />
      </div>

      <div className="absolute bottom-4 lg:bottom-40 z-10">
        <div className="flex items-center justify-center">
          <span className="text-sm text-gray-400 mr-2">by</span>
          <div className="hidden lg:block">
            <Image
              src="/avzdax-logo.svg"
              alt="AVZDAX Logo"
              width={205}
              height={65}
              priority
            />
          </div>
          <div className="lg:hidden">
            <Image
              src="/avzdax-logo.svg"
              alt="AVZDAX Logo"
              width={120}
              height={65}
              priority
            />
          </div>
        </div>
      </div>
    </div>
  );
}

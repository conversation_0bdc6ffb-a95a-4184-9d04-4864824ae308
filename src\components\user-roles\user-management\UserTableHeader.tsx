"use client";
import React from "react";

interface UserTableHeaderProps {
  selectedCount: number;
  totalCount: number;
  onSelectAll: () => void;
}

const UserTableHeader: React.FC<UserTableHeaderProps> = ({
  selectedCount,
  totalCount,
  onSelectAll,
}) => {
  return (
    <div
      className="grid gap-2 px-6 py-4 bg-[#1F1F1F] border-b border-[#3D3D3D]"
      style={{ gridTemplateColumns: "40px 150px 1fr 120px 120px 150px" }}
    >
      <div className="flex items-center">
        <input
          type="checkbox"
          checked={selectedCount === totalCount && totalCount > 0}
          onChange={onSelectAll}
          className="w-4 h-4 bg-[#3D3D3D] border border-[#5D5D5D] rounded focus:ring-[#E4E7EC] focus:ring-2"
          aria-label="Select all users"
        />
      </div>
      <div className="text-sm font-medium text-[#E4E7EC]">Name</div>
      <div className="text-sm font-medium text-[#E4E7EC]">Email</div>
      <div className="text-sm font-medium text-[#E4E7EC]">Role</div>
      <div className="text-sm font-medium text-[#E4E7EC]">Status</div>
      <div className="text-sm font-medium text-[#E4E7EC]">Date Added</div>
    </div>
  );
};

export default UserTableHeader;

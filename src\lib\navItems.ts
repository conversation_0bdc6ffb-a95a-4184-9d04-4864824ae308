import { TbDeviceCctv, TbLogs } from "react-icons/tb";
import { IoSettingsOutline } from "react-icons/io5";
import { SlLock } from "react-icons/sl";

export const navGroups = [
  {
    tag: "Surveillance",
    items: [
      {
        href: "/dashboard/security-cameras",
        icon: TbDeviceCctv,
        label: "Security Cameras",
      },
      {
        href: "/dashboard/recorded-footage",
        image: "/rf.svg",
        activeImage: "/rf-atv.svg",
        label: "Recorded Footage",
      },
    ],
  },
  {
    tag: "Alerts & Log",
    items: [
      {
        href: "/dashboard/security-alerts",
        image: "/sa.svg",
        activeImage: "/sa-atv.svg",
        label: "Security Alerts",
      },
      { href: "/dashboard/alert-log", icon: TbLogs, label: "Alert Log" },
    ],
  },
  {
    tag: "Access Control",
    items: [
      {
        href: "/dashboard/manage-access",
        icon: SlLock,
        label: "Manage Access",
      },
    ],
  },
  {
    tag: "Teams",
    items: [
      {
        href: "/dashboard/user-roles",
        icon: SlLock,
        label: "User & Roles",
      },
    ],
  },
  {
    tag: "Settings",
    items: [
      {
        href: "/dashboard/system-settings",
        icon: IoSettingsOutline,
        label: "System Settings",
      },
      {
        href: "/dashboard/device-management",
        image: "/dm.svg",
        activeImage: "/dm.svg",
        label: "Device Management",
      },
    ],
  },
];

"use client";
import React from "react";
import { CiSearch } from "react-icons/ci";
import { IoChevronDown, IoClose } from "react-icons/io5";
import { SearchInputProps } from "@/types/filterTypes";

const SearchInput: React.FC<SearchInputProps> = ({
  searchTerm,
  setSearchTerm,
  isSearchDropdownOpen,
  setIsSearchDropdownOpen,
  activeSecondaryTab,
  onReset,
}) => {
  const getSearchFilterDisplay = () => {
    if (activeSecondaryTab === "all") {
      return "All";
    }
    switch (activeSecondaryTab) {
      case "camera":
        return "Camera Name";
      case "location":
        return "Location";
      case "tags":
        return "Tags";
      default:
        return "All";
    }
  };

  return (
    <div className="relative lg:w-1/3">
      <div className="flex items-center bg-[#1F1F1F] border border-[#4A4A4A]">
        <div className="flex items-center px-3 py-3 flex-1">
          {isSearchDropdownOpen ? (
            <IoClose
              size={26}
              className="text-[#8A8A8A] mr-2 cursor-pointer hover:text-white transition-colors"
              onClick={() => {
                setIsSearchDropdownOpen(false);
                onReset();
              }}
            />
          ) : (
            <CiSearch size={26} className="text-[#8A8A8A] mr-2" />
          )}
          <input
            type="text"
            placeholder="Search for Footage"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="bg-transparent text-white placeholder:text-sm placeholder-[#8A8A8A] focus:outline-none w-full"
          />
        </div>
        <div
          onClick={() => setIsSearchDropdownOpen(!isSearchDropdownOpen)}
          className="flex gap-1.5 cursor-pointer items-center pr-2"
        >
          <div className="flex items-center px-2 py-2 gradient-border cursor-pointer bg-[#2E2E2E] min-w-[50px] justify-center">
            <span className="text-[#BFBFBF] text-xs mr-1">
              {getSearchFilterDisplay()}
            </span>
          </div>
          <IoChevronDown
            size={16}
            className={`text-[#BFBFBF] transition-transform ${
              isSearchDropdownOpen ? "rotate-180" : ""
            }`}
          />
        </div>
      </div>
    </div>
  );
};

export default SearchInput;

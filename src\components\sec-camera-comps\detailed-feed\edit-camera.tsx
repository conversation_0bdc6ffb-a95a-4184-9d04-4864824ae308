"use client";
import React, { useState, useEffect } from "react";
import { DetailedModal } from "@/components/Modal";
import { EditableTextField } from "@/components/InputField";
import { Camera } from "@/lib/cameras";

interface EditCameraModalProps {
  isOpen: boolean;
  onClose: () => void;
  camera: Camera | null;
  onUpdate?: (updatedCamera: Camera) => void;
  refreshCameras?: () => void;
}

const EditCameraModal: React.FC<EditCameraModalProps> = ({
  isOpen,
  onClose,
  camera,
  onUpdate,
  refreshCameras,
}) => {
  const [formData, setFormData] = useState({ name: "", location: "" });
  const [errors, setErrors] = useState({ name: "", location: "" });
  const [focusedField, setFocusedField] = useState<string | null>(null);

  useEffect(() => {
    if (camera) {
      setFormData({ name: camera.name, location: camera.location });
    }
  }, [camera]);

  const handleChange = (id: string, value: string) => {
    console.log(`[EditCameraModal] handleChange called for ${id}, value: ${value}`);

    const error = value.trim() ? "" : `${id.charAt(0).toUpperCase() + id.slice(1)} is required`;
    setErrors((prev) => ({ ...prev, [id]: error }));

    if (error) {
      console.log(`[EditCameraModal] Validation failed for ${id}: ${error}`);
      return;
    }

    const newFormData = { ...formData, [id]: value };
    setFormData(newFormData);
    console.log(`[EditCameraModal] Updating formData for ${id}: ${value}`);

    if (camera) {
      const updatedCamera = { ...camera, [id]: value };

      if (onUpdate) {
        onUpdate(updatedCamera);
        console.log(`[EditCameraModal] Notified parent (DetailedFeed) of update for ${id}`);
      }

      if (refreshCameras) {
        refreshCameras();
        console.log(`[EditCameraModal] Triggered refreshCameras for ${id}`);
      }
    }
  };

  const handleFocus = (field: string) => {
    setFocusedField(field);
  };

  const handleBlur = () => {
    setFocusedField(null);
  };

  return (
    <DetailedModal isOpen={isOpen} onClose={onClose}>
      <h2 className="text-lg lg:text-xl text-white font-medium mb-6 border-b border-[#3D3D3D] pb-4">
        Edit Camera Settings
      </h2>
      <div className="space-y-6">
        <EditableTextField
          id="name"
          label="Camera Name"
          value={formData.name}
          onChange={(value) => handleChange("name", value)}
          error={errors.name}
          isFocused={focusedField === "name"}
          submitAttempted={false}
          onFocus={handleFocus}
          onBlur={handleBlur}
        />
        <EditableTextField
          id="location"
          label="Camera Location"
          value={formData.location}
          onChange={(value) => handleChange("location", value)}
          error={errors.location}
          isFocused={focusedField === "location"}
          submitAttempted={false}
          onFocus={handleFocus}
          onBlur={handleBlur}
        />
      </div>
    </DetailedModal>
  );
};

export default EditCameraModal;
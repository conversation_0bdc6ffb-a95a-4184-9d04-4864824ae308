"use client";
import React, { useState } from "react";
import { TextField } from "@/components/InputField";
import { SubmitButton } from "@/components/button";
import { useUserForm } from "@/hooks/useUserForm";
import { User } from "@/types/userTypes";
import RoleDropdown from "./RoleDropdown";

interface UserFormProps {
  editUser?: User | null;
  onSubmit: (userData: User) => Promise<void>;
  isEditMode: boolean;
}

const UserForm: React.FC<UserFormProps> = ({
  editUser,
  onSubmit,
  isEditMode,
}) => {
  const [isRoleDropdownOpen, setIsRoleDropdownOpen] = useState(false);

  const {
    formData,
    errors,
    focusedField,
    submitAttempted,
    handleChange,
    handleRoleChange,
    handleFocus,
    handleBlur,
    handleSubmit,
    isFormValid,
  } = useUserForm(onSubmit, editUser);

  const handleRoleSelect = (role: string) => {
    handleRoleChange(role);
    setIsRoleDropdownOpen(false);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Name Field */}
      <TextField
        id="name"
        label="Name"
        type="text"
        placeholder="Enter Name"
        value={formData.name}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        error={errors.name}
        isFocused={focusedField === "name"}
        submitAttempted={submitAttempted}
        showErrorAlways={true}
      />

      {/* Email Field */}
      <TextField
        id="email"
        label="Email"
        type="email"
        placeholder="Enter Email"
        value={formData.email}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        error={errors.email}
        isFocused={focusedField === "email"}
        submitAttempted={submitAttempted}
        showErrorAlways={true}
      />

      {/* Organization Name Field - Only show for new users (invitations) */}
      {!isEditMode && (
        <TextField
          id="organizationName"
          label="Organization Name"
          type="text"
          placeholder="Enter Organization Name"
          value={formData.organizationName}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          error={errors.organizationName}
          isFocused={focusedField === "organizationName"}
          submitAttempted={submitAttempted}
          showErrorAlways={true}
        />
      )}

      {/* Role Dropdown */}
      <RoleDropdown
        selectedRole={formData.role}
        isOpen={isRoleDropdownOpen}
        onToggle={() => setIsRoleDropdownOpen(!isRoleDropdownOpen)}
        onRoleSelect={handleRoleSelect}
        error={errors.role}
        isFocused={focusedField}
        submitAttempted={submitAttempted}
      />

      {/* Submit Button */}
      <div className="flex justify-center w-full pt-4">
        <SubmitButton
          label={isEditMode ? "Save Changes" : "Add User"}
          type="submit"
          disabled={!isFormValid}
          className={!isFormValid ? "opacity-50 cursor-not-allowed" : ""}
        />
      </div>
    </form>
  );
};

export default UserForm;

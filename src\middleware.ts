import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Get the auth token from cookies
  const authToken = request.cookies.get('authToken')?.value;
  
  // Define protected routes
  const protectedRoutes = ['/dashboard'];
  const authRoutes = ['/auth'];
  
  // Check if the current path is protected
  const isProtectedRoute = protectedRoutes.some(route => 
    pathname.startsWith(route)
  );
  
  // Check if the current path is an auth route
  const isAuthRoute = authRoutes.some(route => 
    pathname.startsWith(route)
  );
  
  // If user is not authenticated and trying to access protected route
  if (isProtectedRoute && !authToken) {
    const url = request.nextUrl.clone();
    url.pathname = '/auth';
    return NextResponse.redirect(url);
  }
  
  // If user is authenticated and trying to access auth routes
  if (isAuthRoute && authToken) {
    const url = request.nextUrl.clone();
    url.pathname = '/dashboard/security-cameras';
    return NextResponse.redirect(url);
  }
  
  // For first-time users (no auth token), redirect to auth
  if (pathname === '/' && !authToken) {
    const url = request.nextUrl.clone();
    url.pathname = '/auth';
    return NextResponse.redirect(url);
  }
  
  // For authenticated users on root, redirect to dashboard
  if (pathname === '/' && authToken) {
    const url = request.nextUrl.clone();
    url.pathname = '/dashboard/security-cameras';
    return NextResponse.redirect(url);
  }
  
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};

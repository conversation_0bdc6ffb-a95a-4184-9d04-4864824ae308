"use client";
import React from "react";
import { SubmitButton } from "@/components/button";
import { TextField } from "@/components/InputField";
import { useManualSetupForm } from "./hooks";

interface ManualSetupFormProps {
  onSubmit: (formData: {
    ipAddress: string;
    location: string;
  }) => void;
  error?: string;
}

const ManualSetupForm: React.FC<ManualSetupFormProps> = ({ onSubmit, error }) => {
  const {
    formData,
    errors,
    focusedField,
    submitAttempted,
    handleChange,
    handleSubmit,
    handleFocus,
    handleBlur,
  } = useManualSetupForm(onSubmit);

  return (
    <div className="w-full">
      <div className="flex justify-between border-b border-[#3D3D3D] pb-5 items-center mb-8">
        <h1 className="font-medium text-lg text-[#E4E7EC]">Manual Setup</h1>
      </div>

      {error && (
        <p className="text-red-500 text-xs mb-4">{error}</p>
      )}

      <div className="space-y-6">
        <TextField
          id="ipAddress"
          label="Camera ID/IP Address"
          type="text"
          placeholder="e.g., ************* or CAM001"
          value={formData.ipAddress}
          onChange={handleChange}
          onFocus={() => handleFocus("ipAddress")}
          onBlur={handleBlur}
          error={errors.ipAddress}
          isFocused={focusedField === "ipAddress"}
          submitAttempted={submitAttempted}
          showErrorAlways={true}
        />

        <TextField
          id="location"
          label="Attach Location"
          type="text"
          placeholder="Enter Location of Camera"
          value={formData.location}
          onChange={handleChange}
          onFocus={() => handleFocus("location")}
          onBlur={handleBlur}
          error={errors.location}
          isFocused={focusedField === "location"}
          submitAttempted={submitAttempted}
        />
      </div>
      <div className="flex justify-center w-full mt-6">
        <SubmitButton label="Connect Camera" onClick={handleSubmit} />
      </div>
    </div>
  );
};

export default ManualSetupForm;
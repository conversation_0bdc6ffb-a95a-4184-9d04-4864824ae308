"use client";
import React, { useMemo } from "react";
import { useRouter } from "next/navigation";
import FootageCard from "./FootageCard";
import FootageSearchFilterBar from "./filter-bar/FootageSearchFilterBar";
import { RecordedFootage } from "@/lib/recordedFootage";

interface RecordedFootageGridProps {
  footage: RecordedFootage[];
  locations: string[];
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  selectedTimeRange: string;
  setSelectedTimeRange: (range: string) => void;
  selectedFaceRecognition: string;
  setSelectedFaceRecognition: (option: string) => void;
  selectedDateRange: { startDate: Date | null; endDate: Date | null };
  setSelectedDateRange: (range: { startDate: Date | null; endDate: Date | null }) => void;
  activeSecondaryTab: string;
  setActiveSecondaryTab: (tab: string) => void;
  selectedCameras: string[];
  setSelectedCameras: (cameras: string[]) => void;
  selectedLocations: string[];
  setSelectedLocations: (locations: string[]) => void;
  selectedTags: string[];
  setSelectedTags: (tags: string[]) => void;
  selectedCustomTimeRange: { startTime: string | null; endTime: string | null };
  setSelectedCustomTimeRange: (range: { startTime: string | null; endTime: string | null }) => void;
}

const RecordedFootageGrid: React.FC<RecordedFootageGridProps> = ({
  footage,
  searchTerm,
  setSearchTerm,
  selectedTimeRange,
  setSelectedTimeRange,
  selectedFaceRecognition,
  setSelectedFaceRecognition,
  selectedDateRange,
  setSelectedDateRange,
  activeSecondaryTab,
  setActiveSecondaryTab,
  selectedCameras,
  setSelectedCameras,
  selectedLocations,
  setSelectedLocations,
  selectedTags,
  setSelectedTags,
  selectedCustomTimeRange,
  setSelectedCustomTimeRange,
}) => {
  const router = useRouter();

  const groupedFootageByCamera = useMemo(() => {
    const grouped: { [key: string]: RecordedFootage[] } = {};

    footage.forEach(item => {
      if (!grouped[item.cameraName]) {
        grouped[item.cameraName] = [];
      }
      grouped[item.cameraName].push(item);
    });

    // Sort footage within each camera by time (newest first)
    Object.keys(grouped).forEach(camera => {
      grouped[camera].sort((a, b) =>
        new Date(b.startTime).getTime() - new Date(a.startTime).getTime()
      );
    });

    return grouped;
  }, [footage]);

  const handleFootageClick = (footageItem: RecordedFootage) => {
    router.push(`/dashboard/recorded-footage/detailed-view/${footageItem.id}`);
  };



  return (
    <div className="w-full">
      <FootageSearchFilterBar
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        selectedTimeRange={selectedTimeRange}
        setSelectedTimeRange={setSelectedTimeRange}
        selectedFaceRecognition={selectedFaceRecognition}
        setSelectedFaceRecognition={setSelectedFaceRecognition}
        selectedDateRange={selectedDateRange}
        setSelectedDateRange={setSelectedDateRange}
        activeSecondaryTab={activeSecondaryTab}
        setActiveSecondaryTab={setActiveSecondaryTab}
        selectedCameras={selectedCameras}
        setSelectedCameras={setSelectedCameras}
        selectedLocations={selectedLocations}
        setSelectedLocations={setSelectedLocations}
        selectedTags={selectedTags}
        setSelectedTags={setSelectedTags}
        selectedCustomTimeRange={selectedCustomTimeRange}
        setSelectedCustomTimeRange={setSelectedCustomTimeRange}
      />

      {/* Footage grouped by camera */}
      <div className="space-y-8">
        {Object.entries(groupedFootageByCamera).map(([cameraName, cameraFootage]) => (
          <div key={cameraName}>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-medium text-white">{cameraName}</h2>
              <button
                type="button"
                className="text-sm text-[#8A8A8A] hover:text-white transition-colors underline"
              >
                See all
              </button>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 border-b pb-5 border-[#3D3D3D]">
              {cameraFootage.map((footageItem) => (
                <FootageCard
                  key={footageItem.id}
                  footage={footageItem}
                  onClick={() => handleFootageClick(footageItem)}
                />
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* No results */}
      {footage.length === 0 && (
        <div className="text-center py-12">
          <div className="text-[#8A8A8A] mb-2">No footage found</div>
          <div className="text-sm text-[#8A8A8A]">
            Try adjusting your search or filter criteria
          </div>
        </div>
      )}


    </div>
  );
};

export default RecordedFootageGrid;

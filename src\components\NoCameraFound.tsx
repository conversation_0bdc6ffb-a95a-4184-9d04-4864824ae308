"use client";
import React from "react";
import { HiOutlineExclamationTriangle } from "react-icons/hi2";

interface NoCameraFoundProps {
  ipAddress?: string;
  onRetry?: () => void;
  className?: string;
}

const NoCameraFound: React.FC<NoCameraFoundProps> = ({
  ipAddress,
  onRetry,
  className = "",
}) => {
  return (
    <div className={`flex flex-col items-center justify-center p-8 text-center ${className}`}>
      <div className="mb-4">
        <HiOutlineExclamationTriangle className="w-16 h-16 text-yellow-500 mx-auto" />
      </div>
      
      <h3 className="text-lg font-medium text-white mb-2">
        No Camera Found
      </h3>
      
      <p className="text-gray-400 text-sm mb-1">
        Unable to connect to camera at:
      </p>
      
      {ipAddress && (
        <p className="text-white font-mono text-sm mb-4 bg-gray-800 px-3 py-1 rounded">
          {ipAddress}
        </p>
      )}
      
      <div className="text-gray-400 text-xs space-y-1 mb-6">
        <p>• Check if the camera is powered on</p>
        <p>• Verify the IP address is correct</p>
        <p>• Ensure camera is on the same network</p>
        <p>• Check camera credentials</p>
      </div>
      
      {onRetry && (
        <button
          type="button"
          onClick={onRetry}
          className="px-4 py-2 bg-blue-600 text-white text-sm rounded transition-colors"
        >
          Try Again
        </button>
      )}
    </div>
  );
};

export default NoCameraFound;

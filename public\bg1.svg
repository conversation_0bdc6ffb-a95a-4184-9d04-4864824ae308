<svg width="1440" height="383" viewBox="0 0 1440 383" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_142_7940)">
<ellipse cx="689.5" cy="-41" rx="806.5" ry="124" fill="#3D3D3D"/>
</g>
<defs>
<filter id="filter0_f_142_7940" x="-417" y="-465" width="2213" height="848" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="150" result="effect1_foregroundBlur_142_7940"/>
</filter>
</defs>
</svg>

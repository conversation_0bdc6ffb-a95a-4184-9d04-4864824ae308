export interface Camera {
  id: string;
  name: string;
  image: string;
  location: string;
  isActive: boolean;
  connectionStatus: string;
  url?: string;
  createdAt?: string;
  updatedAt?: string;
  settings?: {
    thumbnailUrl?: string;
    model?: string;
    manufacturer?: string;
  };
  connectivityStats?: {
    uptimePercentage?: number;
    lastOnline?: string;
    averageResponseTime?: number;
    monitoringSince?: string;
  };
}

export const transformApiCamera = (apiCamera: {
  _id: string;
  name: string;
  url?: string;
  location: string;
  isActive: boolean;
  connectionStatus?: string;
  createdAt?: string;
  updatedAt?: string;
  ipAddress?: string;
  username?: string;
  password?: string;
  port?: number;
  settings?: {
    thumbnailUrl?: string;
    model?: string;
    manufacturer?: string;
  };
  connectivityStats?: {
    uptimePercentage?: number;
    lastOnline?: string;
    averageResponseTime?: number;
    monitoringSince?: string;
  };
}): Camera => {
  let streamUrl = apiCamera.url;
  if (!streamUrl && apiCamera.ipAddress) {
    streamUrl = `rtsp://${apiCamera.username || 'admin'}:${apiCamera.password || 'admin'}@${apiCamera.ipAddress}:${apiCamera.port || 554}/stream`;
  }

  return {
    id: apiCamera._id,
    name: apiCamera.name,
    image: apiCamera.settings?.thumbnailUrl || "/videos/office-lobby.gif",
    location: apiCamera.location,
    isActive: apiCamera.isActive,
    connectionStatus: apiCamera.connectionStatus || 'unknown',
    url: streamUrl,
    createdAt: apiCamera.createdAt,
    updatedAt: apiCamera.updatedAt,
    settings: apiCamera.settings,
    connectivityStats: apiCamera.connectivityStats,
  };
};

export const mockCameras: Camera[] = [
  {
    id: "1",
    name: "Office-2 Lobby",
    image: "/videos/office-lobby.gif",
    location: "Office Building",
    isActive: true,
    connectionStatus: "online"
  },
  {
    id: "2",
    name: "Floor 3 Parking Lot",
    image: "/videos/parking-lot.png",
    location: "Parking Area",
    isActive: true,
    connectionStatus: "online"
  },
  {
    id: "3",
    name: "Factory Site - Entrance",
    image: "/videos/office-lobby.gif",
    location: "Factory",
    isActive: true,
    connectionStatus: "offline"
  },
  {
    id: "4",
    name: "Floor 3 Parking Lot",
    image: "/videos/parking-lot.png",
    location: "Parking Area",
    isActive: false,
    connectionStatus: "offline"
  },
  {
    id: "5",
    name: "Restaurant - Entrance",
    image: "/videos/parking-lot.png",
    location: "Restaurant",
    isActive: true,
    connectionStatus: "unstable"
  },
  {
    id: "6",
    name: "Backyard - Factory site",
    image: "/videos/parking-lot.png",
    location: "Factory",
    isActive: true,
    connectionStatus: "online"
  },
];

export interface User {
  id: string;
  name: string;
  email: string;
  role: "Admin" | "User" | "Security" | "Audit";
  status: "Active" | "Inactive" | "Pending";
  dateAdded: string;
  organizationName?: string; // Optional field for invitation scenarios
}

export interface UserFormData {
  name: string;
  email: string;
  role: "Admin" | "User" | "Security" | "Audit" | "";
  organizationName: string;
}

export interface UserFormErrors {
  name: string;
  email: string;
  role: string;
  organizationName: string;
}

export interface AddUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUserAdded: (userData: User) => Promise<boolean>;
  onSuccessComplete: () => void;
  editUser?: User | null; // Optional prop for editing existing user
}

export interface UserManagementViewProps {
  users: User[];
  pendingInvitations: number;
  pendingInvitationsList?: ApiInvitation[];
  onAddUser: () => void;
  onEditUser: (user: User) => void;
  onRemoveUser: (userId: string) => void;
  onRevokeInvitation: (invitationId: string) => Promise<boolean>;
  onResendInvitation: (invitationId: string) => Promise<boolean>;
  loading: boolean;
  isAdmin: boolean;
}

// API transformation helper types
export interface ApiUserTransform {
  fromApiTeamMember: (apiMember: ApiTeamMember) => User;
  fromApiInvitation: (apiInvitation: ApiInvitation) => User;
}

// Import the API types
export interface ApiTeamMember {
  id: string;
  _id?: string;
  name: string;
  email: string;
  role: "user" | "admin" | "security" | "audit";
  companyName: string;
  joinedAt: string;
  invitedAt: string;
}

export interface ApiInvitation {
  id: string;
  _id?: string; // Keep for backward compatibility
  email: string;
  role: "user" | "admin" | "security" | "audit";
  invitedBy?: string;
  organizationName: string;
  message: string;
  status: "pending" | "accepted" | "expired" | "revoked";
  createdAt: string;
  expiresAt: string;
  isExpired: boolean;
}

"use client";
import React, { useState, useEffect, Suspense } from "react";
import Image from "next/image";
import { useSearchParams } from "next/navigation";
import { useAuthStore } from "@/store/authStore";
import { useAuthValidation } from "./hooks";
import { AuthInputField } from "@/components/InputField";
import { TabButton, SubmitButton } from "@/components/button";
import { ForgotPasswordModal } from "./forgotPassword";
import TeletraanAuthFrame from "@/components/TeletraanAuthFrame";
import Loading from "@/components/loader/loading";
import SkeletonLoading from "@/components/loader/SkeletonLoading";

function AuthForm() {
  const searchParams = useSearchParams();
  const {
    activeTab,
    setActiveTab,
    showPassword,
    setShowPassword,
    showConfirmPassword,
    setShowConfirmPassword,
    formData,
    errors,
    focusedField,
    submitAttempted,
    isAuthenticated,
    invitationData,
  } = useAuthStore();

  const {
    handleChange,
    handleFocus,
    handleBlur,
    handleSubmit,
  } = useAuthValidation();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [showLoading, setShowLoading] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Handle tab parameter from URL
  useEffect(() => {
    if (!isMounted) return;

    const tabParam = searchParams.get("tab");
    if (tabParam === "signin") {
      setActiveTab("Sign In");
    } else if (tabParam === "signup") {
      setActiveTab("Sign Up");
    }
  }, [isMounted, searchParams, setActiveTab]);

  useEffect(() => {
    console.log("Auth page effect - isAuthenticated:", isAuthenticated, "showLoading:", showLoading);
    if (isAuthenticated && !showLoading) {
      console.log("Setting showLoading to true");
      setShowLoading(true);
    }
  }, [isAuthenticated, showLoading]);

  const handleFormSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    console.log("Form submitted, calling handleSubmit");
    const isValid = await handleSubmit(e);
    console.log("handleSubmit returned:", isValid);
    if (isValid) {
      console.log("Form submission valid, setting showLoading to true");
      setShowLoading(true);
    } else {
      console.log("Form submission invalid, not showing loading");
    }
  };

  return (
    <div className="relative flex min-h-screen bg-[#121212] text-white">
      {(showLoading || isAuthenticated) && (
        <div className="absolute inset-0 z-50">
          <Loading route={true} destination="/dashboard" />
        </div>
      )}

      {!showLoading && !isAuthenticated && (
        <>
          <div className="absolute top-0 right-0 w-[70%] h-[60%] overflow-hidden">
            <Image
              src="/bg-auth1.svg"
              alt="Background Ellipse 1"
              layout="fill"
              objectFit="cover"
              className="opacity-99"
            />
          </div>
          <div className="hidden md:flex w-full flex-col items-center justify-center p-4 relative z-10">
            <div className="relative 2xl:w-[60%] md:w-[78%] border border-[#3D3D3D] rounded-xl px-14 py-[3rem]">
              <Image
                src="/authvector.svg"
                alt="TELETRAAN Authentication Frame"
                layout="fill"
                className="absolute mt-4 inset-0 object-contain opacity-8"
              />
              <div className="border-b border-t border-[#3D3D3D] py-[3rem] text-center">
                <TeletraanAuthFrame />
                <p className="xxl:text-xl text-lg pt-[3rem]">
                  The Future of AI-Powered Security
                </p>
              </div>
              <div className="absolute bottom-0 right-0 w-full h-[90%] overflow-hidden">
                <Image
                  src="/bg-tele.svg"
                  alt="Background Ellipse"
                  layout="fill"
                  objectFit="cover"
                  className="opacity-90"
                />
              </div>
            </div>
          </div>

          <div className="w-full relative flex flex-col justify-center p-4 md:p-6">
            <div className="w-full max-w-md md:max-w-lg 2xl:max-w-xl mx-auto">
              <h1 className="text-4xl teletraan-title md:text-5xl font-light mb-14 tracking-wider text-center">
                TELETRAAN
              </h1>

              <div className="flex border border-[#3D3D3D] bg-[#1F1F1F] mb-6">
                <TabButton
                  tab="Sign Up"
                  activeTab={activeTab}
                  setActiveTab={setActiveTab}
                />
                <TabButton
                  tab="Sign In"
                  activeTab={activeTab}
                  setActiveTab={setActiveTab}
                />
              </div>

              <form onSubmit={handleFormSubmit} className="space-y-4">
                <AuthInputField
                  id="email"
                  label="Email"
                  type="email"
                  placeholder="Enter Email"
                  value={formData.email}
                  onChange={handleChange}
                  onFocus={handleFocus}
                  onBlur={handleBlur}
                  error={errors.email}
                  isFocused={focusedField === "email"}
                  submitAttempted={submitAttempted}
                  readOnly={isMounted && !!invitationData}
                />

                {/* Show invitation message if present */}
                {invitationData && (
                  <div className="p-3 bg-[#1F1F1F] border border-[#3D3D3D] rounded-lg">
                    <p className="text-sm text-[#A3A3A3] mb-1">
                      You&apos;ve been invited by <span className="text-white font-medium">{invitationData.invitedBy}</span> to join <span className="text-white font-medium">{invitationData.organizationName}</span>
                    </p>
                    <p className="text-xs text-[#A3A3A3] mb-2">{invitationData.message}</p>
                    <p className="text-xs text-[#8A8A8A] italic">
                      Your email has been pre-filled and cannot be changed.
                    </p>
                  </div>
                )}



                <AuthInputField
                  id="password"
                  label="Password"
                  placeholder="Enter Password"
                  value={formData.password}
                  onChange={handleChange}
                  onFocus={handleFocus}
                  onBlur={handleBlur}
                  error={errors.password}
                  isFocused={focusedField === "password"}
                  showToggle
                  isVisible={showPassword}
                  toggleVisibility={() => setShowPassword(!showPassword)}
                  submitAttempted={submitAttempted}
                />

                {activeTab === "Sign Up" && (
                  <AuthInputField
                    id="confirmPassword"
                    label="Confirm Password"
                    placeholder="Enter Password"
                    value={formData.confirmPassword}
                    onChange={handleChange}
                    onFocus={handleFocus}
                    onBlur={handleBlur}
                    error={errors.confirmPassword}
                    isFocused={focusedField === "confirmPassword"}
                    showToggle
                    isVisible={showConfirmPassword}
                    toggleVisibility={() =>
                      setShowConfirmPassword(!showConfirmPassword)
                    }
                    submitAttempted={submitAttempted}
                  />
                )}

                {activeTab === "Sign In" && (
                  <div>
                    <a
                      className="text-sm cursor-pointer text-[#A3A3A3] underline"
                      onClick={(e) => {
                        e.preventDefault();
                        setIsModalOpen(true);
                      }}
                    >
                      Forgot Password?
                    </a>
                  </div>
                )}

                <div className="flex justify-center">
                  <SubmitButton label={activeTab} />
                </div>
              </form>
              <ForgotPasswordModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
              />
            </div>
          </div>
        </>
      )}
    </div>
  );
}

export default function Auth() {
  return (
    <Suspense fallback={<SkeletonLoading />}>
      <AuthForm />
    </Suspense>
  );
}

import { useRouter, useParams } from "next/navigation";
import { useState, useEffect } from "react";
import { Camera } from "@/lib/cameras";
import { Alert, alerts } from "@/lib/alerts";
import { useCameras } from "@/hooks/useCameras";

export const useDetailedFeedLogic = () => {
  const router = useRouter();
  const params = useParams();
  const { id } = params as { id: string };
  const { addedCameras } = useCameras();
  const [selectedCamera, setSelectedCamera] = useState<Camera | null>(null);
  const [alertsState, setAlerts] = useState<Alert[]>([]);
  const [loading, setLoading] = useState(true);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDisconnectModalOpen, setIsDisconnectModalOpen] = useState(false);

  const formatDate = (dateStr: string): string => {
    const [month, day, year] = dateStr.split("-").map(Number);
    const fullYear = 2000 + year;
    const date = new Date(fullYear, month - 1, day);
    return date.toLocaleDateString("en-US", {
      month: "long",
      day: "2-digit",
      year: "numeric",
    });
  };

  const isToday = (dateStr: string): boolean => {
    const today = new Date("2025-05-29T12:16:00+00:00"); 
    const [month, day, year] = dateStr.split("-").map(Number);
    const fullYear = 2000 + year;
    const date = new Date(fullYear, month - 1, day);
    return (
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
    );
  };

  const groupedAlerts = alertsState.reduce((acc, alert) => {
    const dateKey = alert.date;
    if (!acc[dateKey]) {
      acc[dateKey] = [];
    }
    acc[dateKey].push(alert);
    return acc;
  }, {} as Record<string, Alert[]>);

  const getAlertIcon = (type: string): string => {
    const typePrefix = type.split(" ")[0].toLowerCase();
    switch (typePrefix) {
      case "motion":
        return "/motion.svg";
      case "door":
        return "/door.svg";
      case "unknown":
        return "/unknown.svg";
      default:
        return "/motion.svg";
    }
  };

  const handleBack = () => {
    router.push("/dashboard/security-cameras");
  };

  const handleEditCamera = () => {
    setIsEditModalOpen(true);
  };

  const handleDisconnectCamera = () => {
    setIsDisconnectModalOpen(true);
  };

  useEffect(() => {
    if (!id) {
      setLoading(false);
      return;
    }

    console.log("Dynamic Route ID:", id);

    const camera = addedCameras.find((cam) => cam.id.toString() === id);
    console.log("Found Camera:", camera);

    setSelectedCamera(camera || null);
    setAlerts(alerts);

    setLoading(false);
  }, [id, addedCameras]);

  return {
    selectedCamera,
    setSelectedCamera,
    groupedAlerts,
    loading,
    formatDate,
    isToday,
    getAlertIcon,
    handleBack,
    handleEditCamera,
    handleDisconnectCamera,
    isEditModalOpen,
    setIsEditModalOpen,
    isDisconnectModalOpen,
    setIsDisconnectModalOpen,
  };
};
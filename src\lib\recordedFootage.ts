export interface RecordedFootage {
  id: string;
  cameraId: string;
  cameraName: string;
  location: string;
  thumbnailUrl: string;
  videoUrl: string;
  startTime: string;
  endTime: string;
  duration: number; // in seconds
  fileSize: number; // in MB
  recordingType: 'motion' | 'scheduled' | 'manual' | 'alert';
  hasAudio: boolean;
  resolution: string;
  faceDetected: boolean;
  motionDetected: boolean;
  alertTriggered: boolean;
  tags: string[];
  createdAt: string;
}

export const mockRecordedFootage: RecordedFootage[] = [
  {
    id: "rf_001",
    cameraId: "1",
    cameraName: "Front Gate Cam",
    location: "Main Entrance",
    thumbnailUrl: "/videos/office-lobby.gif",
    videoUrl: "/videos/sample-footage-1.mp4",
    startTime: "2025-01-15T08:30:00Z",
    endTime: "2025-01-15T08:35:00Z",
    duration: 300,
    fileSize: 45.2,
    recordingType: "motion",
    hasAudio: true,
    resolution: "1920x1080",
    faceDetected: true,
    motionDetected: true,
    alertTriggered: false,
    tags: ["person", "vehicle"],
    createdAt: "2025-01-15T08:30:00Z"
  },
  {
    id: "rf_002",
    cameraId: "2",
    cameraName: "Back Camera",
    location: "Back Yard",
    thumbnailUrl: "/videos/parking-lot.png",
    videoUrl: "/videos/sample-footage-2.mp4",
    startTime: "2025-01-15T08:30:02Z",
    endTime: "2025-01-15T08:35:00Z",
    duration: 298,
    fileSize: 52.8,
    recordingType: "scheduled",
    hasAudio: false,
    resolution: "1920x1080",
    faceDetected: false,
    motionDetected: true,
    alertTriggered: false,
    tags: ["vehicle", "parking"],
    createdAt: "2025-01-15T08:30:02Z"
  },
  {
    id: "rf_003",
    cameraId: "1",
    cameraName: "Front Gate Cam",
    location: "Main Entrance",
    thumbnailUrl: "/videos/office-lobby.gif",
    videoUrl: "/videos/sample-footage-3.mp4",
    startTime: "2025-01-15T08:30:02Z",
    endTime: "2025-01-15T08:45:00Z",
    duration: 898,
    fileSize: 125.4,
    recordingType: "alert",
    hasAudio: true,
    resolution: "1920x1080",
    faceDetected: true,
    motionDetected: true,
    alertTriggered: true,
    tags: ["unknown_face", "motion"],
    createdAt: "2025-01-15T08:30:02Z"
  },
  {
    id: "rf_004",
    cameraId: "2",
    cameraName: "Back Camera",
    location: "Back Yard",
    thumbnailUrl: "/videos/parking-lot.png",
    videoUrl: "/videos/sample-footage-4.mp4",
    startTime: "2025-01-15T08:30:02Z",
    endTime: "2025-01-15T08:45:00Z",
    duration: 898,
    fileSize: 89.6,
    recordingType: "motion",
    hasAudio: false,
    resolution: "1280x720",
    faceDetected: false,
    motionDetected: true,
    alertTriggered: false,
    tags: ["motion"],
    createdAt: "2025-01-15T08:30:02Z"
  },
  {
    id: "rf_005",
    cameraId: "2",
    cameraName: "Back Camera",
    location: "Back Yard",
    thumbnailUrl: "/videos/office-lobby.gif",
    videoUrl: "/videos/sample-footage-5.mp4",
    startTime: "2025-01-15T08:30:02Z",
    endTime: "2025-01-15T08:45:00Z",
    duration: 898,
    fileSize: 156.7,
    recordingType: "scheduled",
    hasAudio: true,
    resolution: "1920x1080",
    faceDetected: true,
    motionDetected: false,
    alertTriggered: false,
    tags: ["scheduled", "person"],
    createdAt: "2025-01-15T08:30:02Z"
  },
  {
    id: "rf_006",
    cameraId: "1",
    cameraName: "Front Gate Cam",
    location: "Main Entrance",
    thumbnailUrl: "/videos/parking-lot.png",
    videoUrl: "/videos/sample-footage-6.mp4",
    startTime: "2025-01-15T08:30:02Z",
    endTime: "2025-01-15T08:45:00Z",
    duration: 898,
    fileSize: 78.3,
    recordingType: "manual",
    hasAudio: false,
    resolution: "1920x1080",
    faceDetected: false,
    motionDetected: false,
    alertTriggered: false,
    tags: ["manual"],
    createdAt: "2025-01-15T08:30:02Z"
  },
  {
    id: "rf_007",
    cameraId: "2",
    cameraName: "Back Camera",
    location: "Back Yard",
    thumbnailUrl: "/videos/office-lobby.gif",
    videoUrl: "/videos/sample-footage-7.mp4",
    startTime: "2025-01-14T08:30:02Z",
    endTime: "2025-01-14T08:45:00Z",
    duration: 898,
    fileSize: 92.1,
    recordingType: "motion",
    hasAudio: true,
    resolution: "1920x1080",
    faceDetected: true,
    motionDetected: true,
    alertTriggered: true,
    tags: ["motion", "person", "alert"],
    createdAt: "2025-01-14T08:30:02Z"
  },
  {
    id: "rf_008",
    cameraId: "2",
    cameraName: "Back Camera",
    location: "Back Yard",
    thumbnailUrl: "/videos/parking-lot.png",
    videoUrl: "/videos/sample-footage-8.mp4",
    startTime: "2025-01-14T08:30:02Z",
    endTime: "2025-01-14T08:45:00Z",
    duration: 898,
    fileSize: 67.9,
    recordingType: "scheduled",
    hasAudio: false,
    resolution: "1280x720",
    faceDetected: false,
    motionDetected: false,
    alertTriggered: false,
    tags: ["scheduled"],
    createdAt: "2025-01-14T08:30:02Z"
  },
  {
    id: "rf_009",
    cameraId: "1",
    cameraName: "Front Gate Cam",
    location: "Main Entrance",
    thumbnailUrl: "/videos/office-lobby.gif",
    videoUrl: "/videos/sample-footage-9.mp4",
    startTime: "2025-01-15T09:15:00Z",
    endTime: "2025-01-15T09:20:00Z",
    duration: 300,
    fileSize: 42.1,
    recordingType: "motion",
    hasAudio: true,
    resolution: "1920x1080",
    faceDetected: true,
    motionDetected: true,
    alertTriggered: false,
    tags: ["person", "motion"],
    createdAt: "2025-01-15T09:15:00Z"
  },
  {
    id: "rf_010",
    cameraId: "2",
    cameraName: "Back Camera",
    location: "Back Yard",
    thumbnailUrl: "/videos/parking-lot.png",
    videoUrl: "/videos/sample-footage-10.mp4",
    startTime: "2025-01-15T09:20:00Z",
    endTime: "2025-01-15T09:25:00Z",
    duration: 300,
    fileSize: 38.7,
    recordingType: "motion",
    hasAudio: false,
    resolution: "1920x1080",
    faceDetected: false,
    motionDetected: true,
    alertTriggered: false,
    tags: ["motion", "animal"],
    createdAt: "2025-01-15T09:20:00Z"
  },
  {
    id: "rf_011",
    cameraId: "1",
    cameraName: "Front Gate Cam",
    location: "Main Entrance",
    thumbnailUrl: "/videos/office-lobby.gif",
    videoUrl: "/videos/sample-footage-11.mp4",
    startTime: "2025-01-15T10:30:00Z",
    endTime: "2025-01-15T10:35:00Z",
    duration: 300,
    fileSize: 45.8,
    recordingType: "scheduled",
    hasAudio: true,
    resolution: "1920x1080",
    faceDetected: false,
    motionDetected: false,
    alertTriggered: false,
    tags: ["scheduled"],
    createdAt: "2025-01-15T10:30:00Z"
  },
  {
    id: "rf_012",
    cameraId: "2",
    cameraName: "Back Camera",
    location: "Back Yard",
    thumbnailUrl: "/videos/parking-lot.png",
    videoUrl: "/videos/sample-footage-12.mp4",
    startTime: "2025-01-15T10:45:00Z",
    endTime: "2025-01-15T10:50:00Z",
    duration: 300,
    fileSize: 41.2,
    recordingType: "alert",
    hasAudio: true,
    resolution: "1920x1080",
    faceDetected: true,
    motionDetected: true,
    alertTriggered: true,
    tags: ["unknown_face", "alert"],
    createdAt: "2025-01-15T10:45:00Z"
  },
  {
    id: "rf_013",
    cameraId: "1",
    cameraName: "Front Gate Cam",
    location: "Main Entrance",
    thumbnailUrl: "/videos/office-lobby.gif",
    videoUrl: "/videos/sample-footage-13.mp4",
    startTime: "2025-01-15T11:00:00Z",
    endTime: "2025-01-15T11:05:00Z",
    duration: 300,
    fileSize: 47.3,
    recordingType: "motion",
    hasAudio: true,
    resolution: "1920x1080",
    faceDetected: true,
    motionDetected: true,
    alertTriggered: false,
    tags: ["person", "vehicle"],
    createdAt: "2025-01-15T11:00:00Z"
  },
  {
    id: "rf_014",
    cameraId: "2",
    cameraName: "Back Camera",
    location: "Back Yard",
    thumbnailUrl: "/videos/parking-lot.png",
    videoUrl: "/videos/sample-footage-14.mp4",
    startTime: "2025-01-15T11:15:00Z",
    endTime: "2025-01-15T11:20:00Z",
    duration: 300,
    fileSize: 39.6,
    recordingType: "manual",
    hasAudio: false,
    resolution: "1920x1080",
    faceDetected: false,
    motionDetected: false,
    alertTriggered: false,
    tags: ["manual"],
    createdAt: "2025-01-15T11:15:00Z"
  },
  {
    id: "rf_015",
    cameraId: "1",
    cameraName: "Front Gate Cam",
    location: "Main Entrance",
    thumbnailUrl: "/videos/office-lobby.gif",
    videoUrl: "/videos/sample-footage-15.mp4",
    startTime: "2025-01-15T12:00:00Z",
    endTime: "2025-01-15T12:05:00Z",
    duration: 300,
    fileSize: 44.9,
    recordingType: "scheduled",
    hasAudio: true,
    resolution: "1920x1080",
    faceDetected: false,
    motionDetected: true,
    alertTriggered: false,
    tags: ["scheduled", "motion"],
    createdAt: "2025-01-15T12:00:00Z"
  },
  {
    id: "rf_016",
    cameraId: "1",
    cameraName: "Front Gate Cam",
    location: "Main Entrance",
    thumbnailUrl: "/videos/office-lobby.gif",
    videoUrl: "/videos/sample-footage-16.mp4",
    startTime: "2025-01-15T12:30:00Z",
    endTime: "2025-01-15T12:35:00Z",
    duration: 300,
    fileSize: 43.1,
    recordingType: "motion",
    hasAudio: true,
    resolution: "1920x1080",
    faceDetected: true,
    motionDetected: true,
    alertTriggered: false,
    tags: ["person", "motion"],
    createdAt: "2025-01-15T12:30:00Z"
  }
];

export const getFootageByDate = (footage: RecordedFootage[]) => {
  const grouped: { [key: string]: RecordedFootage[] } = {};
  
  footage.forEach((item) => {
    const date = new Date(item.startTime).toISOString().split('T')[0];
    if (!grouped[date]) {
      grouped[date] = [];
    }
    grouped[date].push(item);
  });
  
  return grouped;
};

export const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`;
};

export const formatFileSize = (sizeInMB: number): string => {
  if (sizeInMB >= 1024) {
    return `${(sizeInMB / 1024).toFixed(1)} GB`;
  }
  return `${sizeInMB.toFixed(1)} MB`;
};

export const getRecordingTypeColor = (type: RecordedFootage['recordingType']): string => {
  switch (type) {
    case 'motion':
      return 'bg-blue-600';
    case 'scheduled':
      return 'bg-green-600';
    case 'manual':
      return 'bg-gray-600';
    case 'alert':
      return 'bg-red-600';
    default:
      return 'bg-gray-600';
  }
};

export const getRecordingTypeLabel = (type: RecordedFootage['recordingType']): string => {
  switch (type) {
    case 'motion':
      return 'Motion';
    case 'scheduled':
      return 'Scheduled';
    case 'manual':
      return 'Manual';
    case 'alert':
      return 'Alert';
    default:
      return 'Unknown';
  }
};

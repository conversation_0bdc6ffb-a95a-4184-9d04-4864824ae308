"use client";
import React from "react";
import CameraCard from "./CameraCard";
import { useRouter } from "next/navigation";
import SearchFilterBar from "./SearchFilterBar";
import { Camera } from "@/lib/cameras";

interface CameraGridProps {
  cameras: Camera[];
  openModal: () => void;
  locations: string[];
  cameraIds: string[];
  selectedLocation: string;
  setSelectedLocation: (location: string) => void;
  selectedCameraId: string;
  setSelectedCameraId: (id: string) => void;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  refreshCameras?: () => void;
}

const CameraGrid: React.FC<CameraGridProps> = ({
  cameras,
  openModal,
  locations,
  cameraIds,
  selectedLocation,
  setSelectedLocation,
  selectedCameraId,
  setSelectedCameraId,
  searchTerm,
  setSearchTerm,
}) => {
  const router = useRouter();

  const handleCameraClick = (camera: Camera) => {
    router.push(`/dashboard/security-cameras/detailed-feed/${camera.id}`);
  };

  return (
    <div className="w-full">
      <SearchFilterBar
        onAddCamera={openModal}
        locations={locations}
        cameraIds={cameraIds}
        selectedLocation={selectedLocation}
        setSelectedLocation={setSelectedLocation}
        selectedCameraId={selectedCameraId}
        setSelectedCameraId={setSelectedCameraId}
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
      />

      <div className="mb-7">
        <div className="w-[60px] flex cursor-pointer justify-center items-center py-1 border-[0.5px] border-[#8A8A8A] text-[#8A8A8A] text-xs">
          <div className="w-2 h-2 bg-[#8A8A8A] rounded-full mr-1" />
          LIVE
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 cursor-pointer">
        {cameras.map((camera) => (
          <CameraCard
            key={camera.id}
            camera={camera}
            onClick={() => handleCameraClick(camera)}
          />
        ))}
      </div>
    </div>
  );
};

export default CameraGrid;
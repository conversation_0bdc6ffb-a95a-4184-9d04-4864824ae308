"use client";
import React, { useState } from "react";
import AddUserModal from "@/components/user-roles/add-user-modal/AddUserModal";
import UserManagementView from "@/components/user-roles/user-management/UserManagementView";

import { User } from "@/types/userTypes";
import { useUsers } from "@/hooks/useUsers";
import { useRoleAccess } from "@/hooks/useRoleAccess";

export default function UserRoles() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);

  const { isLoading, isAdmin } = useRoleAccess();
  const {
    users,
    pendingInvitations,
    pendingInvitationsList,
    inviteUser,
    updateUser,
    removeUser,
    revokeInvitation,
    resendInvitation,
    loading,
    refreshData
  } = useUsers();


  const handleAddUser = () => {
    setEditingUser(null);
    setIsModalOpen(true);
  };

  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setIsModalOpen(true);
  };

  const handleRemoveUser = async (userId: string) => {
    await removeUser(userId);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingUser(null);
  };

  const handleUserAdded = async (userData: User): Promise<boolean> => {
    if (editingUser) {
      const success = await updateUser(editingUser.id, {
        name: userData.name,
        email: userData.email,
        role: userData.role,
      });

      if (success) {
        setIsModalOpen(false);
        setEditingUser(null);
      }
      return success;
    } else {
      const success = await inviteUser({
        name: userData.name,
        email: userData.email,
        role: userData.role,
        organizationName: userData.organizationName || "",
      });

      return success;
    }
  };

  const handleSuccessComplete = () => {
    setIsModalOpen(false);
    setEditingUser(null);
    refreshData();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <p className="text-[#A3A3A3] text-lg">Loading...</p>
      </div>
    );
  }



  return (
    <>
      <UserManagementView
        users={users}
        pendingInvitations={pendingInvitations}
        pendingInvitationsList={pendingInvitationsList}
        onAddUser={handleAddUser}
        onEditUser={handleEditUser}
        onRemoveUser={handleRemoveUser}
        onRevokeInvitation={revokeInvitation}
        onResendInvitation={resendInvitation}
        loading={loading}
        isAdmin={isAdmin}
      />

      <AddUserModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onUserAdded={handleUserAdded}
        onSuccessComplete={handleSuccessComplete}
        editUser={editingUser}
      />
    </>
  );
}

"use client";
import React from "react";
import { SlLock } from "react-icons/sl";

interface AccessDeniedProps {
  userRole?: string | null;
}

const AccessDenied: React.FC<AccessDeniedProps> = ({ userRole }) => {
  return (
    <div className="flex flex-col items-center justify-center min-h-[60vh] text-center px-4">
      <div className="mb-6">
        <SlLock className="text-6xl text-[#A3A3A3] mx-auto mb-4" />
      </div>
      
      <h2 className="text-2xl font-semibold text-white mb-4">
        Access Restricted
      </h2>
      
      <p className="text-[#A3A3A3] text-lg mb-2 max-w-md">
        You don&apos;t have permission to access user management features.
      </p>
      
      <p className="text-[#A3A3A3] text-sm mb-6 max-w-md">
        Current role: <span className="text-white font-medium capitalize">{userRole || "Unknown"}</span>
      </p>
      
      <p className="text-[#A3A3A3] text-sm max-w-md">
        Contact your administrator if you need access to these features.
      </p>
    </div>
  );
};

export default AccessDenied;

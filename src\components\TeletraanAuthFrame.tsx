"use client";
import React from "react";
import Image from "next/image";

const TeletraanAuthFrame = () => {
  const CircleText = ({ text }: { text: string }) => {
    const characters = text.split("");
    const arcAngle = 197;
    const startAngle = -88;
    const letterHeight = "35%";
    const letterTopMargin = "15%";
    const letterTopPosition = "50";

    return (
      <>
        {characters.map((char, index) => {
          const angle = startAngle + index * (arcAngle / characters.length);
          const style: React.CSSProperties = {
            position: "absolute",
            left: "50%",
            top: letterTopMargin,
            height: letterHeight,
            transform: `rotate(${angle}deg)`,
            transformOrigin: "bottom center",
            width: "10px",
            marginLeft: "-5px",
          };

          return (
            <div key={index} style={style}>
              <span
                className={`absolute top-${letterTopPosition} left-0 right-0 text-center text-white font-bold text-lg md:text-sm`}
              >
                {char}
              </span>
            </div>
          );
        })}
      </>
    );
  };

  const DottedLine = ({
    className,
    style,
  }: {
    className: string;
    style?: React.CSSProperties;
  }) => (
    <div
      className={`absolute border-l border-dashed border-[#A3A3A3] opacity-40 teletraan-pulse-element line ${className}`}
      style={style}
    />
  );

  const dottedLines = [
    { className: "left-[26%] top-9 h-[24%]", key: "left-vert-1" },
    {
      className: "left-[26%] top-[calc(24%+35px)] h-[8%]",
      style: { transform: "rotate(-40deg)", transformOrigin: "top left" },
      key: "left-angle",
    },
    {
      className: "left-[31%] top-[calc(30%+35px)] h-[10%]",
      key: "left-vert-2",
    },
    { className: "right-[26%] top-9 h-[24%]", key: "right-vert-1" },
    {
      className: "right-[26%] top-[calc(24%+35px)] h-[8%]",
      style: { transform: "rotate(40deg)", transformOrigin: "top right" },
      key: "right-angle",
    },
    {
      className: "right-[31%] top-[calc(30%+35px)] h-[10%]",
      key: "right-vert-2",
    },
    { className: "left-1/2 top-[27%] h-[10%]", key: "center-vert-1" },
    { className: "left-1/2 top-[41%] h-[22%]", key: "center-vert-2" },
  ];

  const textBoxes = [
    {
      text: "Maximum Protection",
      wrapperClass: "w-full",
      innerClass: "w-[70%]",
      align: "start",
    },
    {
      text: "AI-Powered Surveillance",
      wrapperClass: "w-full flex justify-end",
      innerClass: "w-[70%]",
      align: "end",
    },
    {
      text: "Access Control",
      wrapperClass: "w-full justify-center items-center flex",
      innerClass: "w-[24%]",
      align: "center",
    },
  ];

  return (
    <div className="relative w-full">
      {dottedLines.map(({ className, style, key }) => (
        <DottedLine key={key} className={className} style={style} />
      ))}

      {/* Text Boxes */}
      <div>
        <div className="w-full flex justify-between items-center">
          {textBoxes.slice(0, 2).map(({ text, wrapperClass, innerClass }) => (
            <div key={text} className={wrapperClass}>
              <div className={innerClass}>
                <div className="text-xs py-2 border bg-[#1F1F1F] text-white border-solid gradient-border teletraan-pulse-element box">
                  {text}
                </div>
              </div>
            </div>
          ))}
        </div>
        <div className="flex justify-center items-center mt-8">
          {textBoxes.slice(2).map(({ text, wrapperClass, innerClass }) => (
            <div key={text} className={wrapperClass}>
              <div className={innerClass}>
                <div className="text-xs py-2 border bg-[#1F1F1F] text-white border-solid gradient-border teletraan-pulse-element box">
                  {text}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Central Circle */}
      <div className="mt-10 flex justify-center">
        <div className="relative w-60 h-60 p-px overflow-visible">
          <div className="absolute inset-0 rounded-full bg-gradient-to-br from-[#707070] via-transparent to-[#E4E7EC] z-10"></div>
          <div className="relative bg-[#1F1F1F] rounded-full w-full h-full flex items-center justify-center z-10">
            <div className="absolute inset-0 z-20">
              <CircleText text="TELETRAAN" />
            </div>
            <div
              className="bg-[#121212] rounded-full w-44 h-44 flex items-center justify-center z-10 teletraan-pulse-circle"
              style={{ boxShadow: "0px 0px 20px 0px #E4E7ECCC" }}
            >
              <div className="relative p-px rounded-xl overflow-hidden">
                <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-[#707070] via-transparent to-[#BFBFBF]"></div>
                <div className="relative py-3 px-3 rounded-xl bg-gradient-to-br from-[#1F1F1F] to-[#707070] bg-[length:400%_400%] bg-[position:0%_0%]">
                  <Image
                    src="/teletraan.svg"
                    alt="Teletraan Logo"
                    width={70}
                    height={70}
                    className="xl:w-[30px] w-[20px]"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TeletraanAuthFrame;

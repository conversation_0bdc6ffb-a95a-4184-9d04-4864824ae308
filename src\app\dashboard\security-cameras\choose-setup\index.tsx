"use client";
import React from "react";
import { CamSetupModal } from "@/components/Modal";
import { SubmitButton } from "@/components/button";
import SetupOption from "@/components/sec-camera-comps/choose-setup-comps/SetupOption";
import ManualSetupForm from "./manual-setup/manual-setup";
import CameraConnectedSuccess from "@/components/CameraConnectedSuccess";
import { motion, AnimatePresence } from "framer-motion";
import { useCameraSetup } from "./hooks";
import { Camera } from "@/lib/cameras";

interface ChooseSetupProps {
  isOpen: boolean;
  onClose: () => void;
  onMethodSelect?: (method: string) => void;
  onCameraAdded?: (camera: Camera) => void;
  addedCameras: Camera[];
}

const ChooseSetup: React.FC<ChooseSetupProps> = ({
  isOpen,
  onClose,
  onMethodSelect,
  onCameraAdded,
  addedCameras,
}) => {
  const {
    selectedMethod,
    currentStep,
    setCurrentStep,
    handleMethodSelect,
    handleContinue,
    handleClose,
    handleManualSetupSubmit,
    handleSuccessComplete,
    error,
  } = useCameraSetup(onClose, onMethodSelect, onCameraAdded, addedCameras);

  const handleSuccessCompleteWithCallback = () => {
    handleSuccessComplete();
  };

  const contentVariants = {
    hidden: { opacity: 0, x: 10 },
    visible: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -10 },
  };

  return (
    <CamSetupModal
      isOpen={isOpen}
      onClose={handleClose}
      showCloseButton={currentStep !== "success"}
    >
      <AnimatePresence mode="wait">
        {currentStep === "choose" && (
          <motion.div
            key="choose"
            className="w-full"
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={contentVariants}
            transition={{ duration: 0.2 }}
          >
            <h1 className="text-2xl font-semibold text-white mb-8">
              Choose Setup Method
            </h1>

            <SetupOption
              title="Smart Setup"
              subtitle="Auto-Detect Nearby Devices"
              method="smart"
              selectedMethod={selectedMethod}
              onSelect={handleMethodSelect}
            />

            <SetupOption
              title="Manual Setup"
              subtitle="Manually Enter Camera ID"
              method="manual"
              selectedMethod={selectedMethod}
              onSelect={handleMethodSelect}
            />

            {selectedMethod && (
              <div className="flex justify-end">
                <SubmitButton label="Continue" onClick={handleContinue} />
              </div>
            )}
          </motion.div>
        )}

        {currentStep === "manual" && (
          <motion.div
            key="manual"
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={contentVariants}
            transition={{ duration: 0.2 }}
          >
            <ManualSetupForm onSubmit={handleManualSetupSubmit} error={error} />
          </motion.div>
        )}

        {currentStep === "success" && (
          <motion.div
            key="success"
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={contentVariants}
            transition={{ duration: 0.2 }}
          >
            <CameraConnectedSuccess
              onComplete={handleSuccessCompleteWithCallback}
            />
          </motion.div>
        )}

        {currentStep === "smart" && (
          <motion.div
            key="smart"
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={contentVariants}
            transition={{ duration: 0.2 }}
            className="w-full"
          >
            <h1 className="text-2xl font-semibold text-white mb-8">
              Smart Setup
            </h1>
            <p className="text-[#8A8A8A]">
              Looking for cameras on your network...
            </p>
            <div className="flex justify-end mt-4">
              <SubmitButton
                label="Back"
                onClick={() => setCurrentStep("choose")}
              />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </CamSetupModal>
  );
};

export default ChooseSetup;
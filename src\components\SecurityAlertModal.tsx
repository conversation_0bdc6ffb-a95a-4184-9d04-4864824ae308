"use client";
import React, { useEffect } from "react";
import { PiWarningBold } from "react-icons/pi";
import { IoIosWarning } from "react-icons/io";
import Image from "next/image";
import { AlertModal } from "@/components/Modal";
import { SubmitButton } from "@/components/button";

interface SecurityAlertModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const alertDetails = [
  {
    label: "Possible Intrusion Detected",
    value: "BURGLARY"
  },
  {
    label: "Camera Name/Location",
    value: "Backyard Factory site- BackCam"
  },
  {
    label: "Detection Type",
    value: "Motion Detected"
  },
  {
    label: "Threat Level",
    value: "High Risk",
    hasIcon: true
  }
];

const SecurityAlertModal: React.FC<SecurityAlertModalProps> = ({
  isOpen,
  onClose,
}) => {
  useEffect(() => {
    if (!isOpen) return;

    const audio = new Audio("/audio/danger-alert.mp3");
    audio.volume = 0.5;
    audio.play().catch((error) => {
      console.warn("Audio playback failed:", error);
    });

    document.body.style.overflow = "hidden";

    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isOpen]);

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") onClose();
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
    };
  }, [isOpen, onClose]);

  return (
    <AlertModal isOpen={isOpen} onClose={onClose} preventOutsideClick={true}>
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Video Section */}
        <div className="w-[55%]">
          <div className="relative h-full bg-gray-900 overflow-hidden border border-gray-700">
            <video
              src="/videos/burglars.mp4"
              width={600}
              height={400}
              className="w-full h-full object-cover"
              autoPlay
              muted
              loop
              playsInline
            >
              Your browser does not support the video tag.
            </video>
            
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 animate-pulse">
              <PiWarningBold size={120} color="red" />
            </div>

            <button
              type="button"
              onClick={onClose}
              className="absolute bottom-4 right-4 bg-[#1F1F1F] gradient-border text-white px-8 py-4 rounded text-sm transition-all cursor-pointer"
            >
              Dismiss Alert
            </button>
          </div>
        </div>

        {/* Alert Details */}
        <div className="w-[45%] space-y-6">
          <div className="flex items-center justify-center gap-5 border border-[#7A271A] p-4">
            <Image
              src="/Alert.svg"
              alt="Alert Icon"
              width={35}
              height={35}
              priority
            />
            <p className="text-[#FDA29B] text-2xl">
              Unauthorized Motion Detected
            </p>
          </div>

          <div className="space-y-8">
            {alertDetails.map((detail, index) => (
              <div key={index} className="border-b border-[#3D3D3D] pb-7">
                <h4 className="text-[#E4E7EC] mb-1">{detail.label}</h4>
                <div className="flex items-center text-xl space-x-2">
                  <span className="text-white font-medium">{detail.value}</span>
                  {detail.hasIcon && <IoIosWarning color="red" size={16} />}
                </div>
              </div>
            ))}
          </div>

          <div className="flex justify-end items-center gap-3 pt-4">
            <button className="border border-[#F97066] text-[#F97066] px-4 py-1.5 h-12 mt-6 flex items-center cursor-pointer">
              Trigger Emergency Response
            </button>
            <SubmitButton label="Watch Live Feed" />
          </div>
        </div>
      </div>
    </AlertModal>
  );
};

export default SecurityAlertModal;
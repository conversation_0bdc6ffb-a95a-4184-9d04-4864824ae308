"use client";
import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { IoChevronBack, IoChevronForward } from "react-icons/io5";
import { SubmitButton } from "@/components/button";
import { DateRangeModalProps } from "@/types/filterTypes";

const DateRangeModal: React.FC<DateRangeModalProps> = ({
  isOpen,
  onClose,
  selectedDateRange,
  onDateRangeSelect,
}) => {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [startDate, setStartDate] = useState<Date | null>(selectedDateRange.startDate);
  const [endDate, setEndDate] = useState<Date | null>(selectedDateRange.endDate);
  const [isSelectingEnd, setIsSelectingEnd] = useState(false);

  const isFutureDate = (date: Date) => {
    const today = new Date();
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const dateStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    return dateStart > todayStart;
  };

  const validateAndFilterDates = (start: Date | null, end: Date | null) => {
    // Filter out any future dates
    const validStart = start && !isFutureDate(start) ? start : null;
    const validEnd = end && !isFutureDate(end) ? end : null;

    // If start date is after end date, swap them
    if (validStart && validEnd && validStart > validEnd) {
      return { startDate: validEnd, endDate: validStart };
    }

    return { startDate: validStart, endDate: validEnd };
  };

  useEffect(() => {
    if (isOpen) {
      // Validate and filter out any future dates that might have been set programmatically
      const validatedRange = validateAndFilterDates(
        selectedDateRange.startDate,
        selectedDateRange.endDate
      );
      setStartDate(validatedRange.startDate);
      setEndDate(validatedRange.endDate);
      setIsSelectingEnd(false);
    }
  }, [isOpen, selectedDateRange]); // eslint-disable-line react-hooks/exhaustive-deps

  const monthNames = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ];

  const dayNames = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }
    
    // Add all days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day));
    }
    
    return days;
  };

  const handleDateClick = (date: Date) => {
    // Prevent selection of future dates
    if (isFutureDate(date)) {
      return;
    }

    if (!startDate || (startDate && endDate) || isSelectingEnd) {
      if (!startDate) {
        setStartDate(date);
        setIsSelectingEnd(true);
      } else if (date < startDate) {
        setStartDate(date);
        setEndDate(null);
        setIsSelectingEnd(true);
      } else {
        setEndDate(date);
        setIsSelectingEnd(false);
      }
    } else {
      if (date < startDate) {
        setStartDate(date);
        setEndDate(null);
        setIsSelectingEnd(true);
      } else {
        setEndDate(date);
        setIsSelectingEnd(false);
      }
    }
  };

  const isDateInRange = (date: Date) => {
    if (!startDate || !endDate) return false;
    return date >= startDate && date <= endDate;
  };

  const isDateSelected = (date: Date) => {
    return (startDate && date.getTime() === startDate.getTime()) ||
           (endDate && date.getTime() === endDate.getTime());
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.getDate() === today.getDate() &&
           date.getMonth() === today.getMonth() &&
           date.getFullYear() === today.getFullYear();
  };

  const handlePrevMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1));
  };

  const handleNextMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1));
  };

  const handleApply = () => {
    const validatedRange = validateAndFilterDates(startDate, endDate);
    onDateRangeSelect(validatedRange);
    onClose();
  };

  const handleClear = () => {
    setStartDate(null);
    setEndDate(null);
    setIsSelectingEnd(false);
  };

  const formatDateRange = () => {
    if (!startDate && !endDate) return "Select date range";
    if (startDate && !endDate) return `${startDate.toLocaleDateString()} - Select end date`;
    if (startDate && endDate) {
      return `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`;
    }
    return "Select date range";
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 bg-transparent flex items-center justify-center backdrop-blur-[6px] z-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          onClick={onClose}
        >
          <motion.div
            className="bg-[#1F1F1F] gradient-border p-6 shadow-lg relative w-full max-w-md mx-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex justify-center items-center gap-4 border-b border-[#3D3D3D] pb-4 mb-6">
              <button
                type="button"
                onClick={handlePrevMonth}
                className="p-2 bg-[#2E2E2E]"
                aria-label="Previous month"
              >
                <IoChevronBack className="text-[#BFBFBF]" size={20} />
              </button>

              <h3 className="text-[#E4E7EC] text-lg font-medium">
                {monthNames[currentMonth.getMonth()]} {currentMonth.getFullYear()}
              </h3>

              <button
                type="button"
                onClick={handleNextMonth}
                className="p-2 bg-[#2E2E2E]"
                aria-label="Next month"
              >
                <IoChevronForward className="text-[#BFBFBF]" size={20} />
              </button>
            </div>

            {/* Day headers */}
            <div className="grid grid-cols-7 gap-1 mb-2">
              {dayNames.map((day) => (
                <div key={day} className="text-center text-[#8A8A8A] text-sm py-2">
                  {day}
                </div>
              ))}
            </div>

            {/* Calendar grid */}
            <div className="grid grid-cols-7 gap-1 mb-6">
              {getDaysInMonth(currentMonth).map((date, index) => (
                <div key={index} className="aspect-square">
                  {date && (
                    <button
                      type="button"
                      onClick={() => handleDateClick(date)}
                      disabled={isFutureDate(date)}
                      className={`w-full h-full flex items-center justify-center text-sm rounded transition-colors relative ${
                        isFutureDate(date)
                          ? "text-[#3A3A3A] bg-[#1A1A1A] cursor-not-allowed opacity-60 border border-[#2A2A2A]"
                          : isDateSelected(date)
                          ? "bg-[#E4E7EC] text-[#1F1F1F] font-medium"
                          : isDateInRange(date)
                          ? "bg-[#3D3D3D] text-[#E4E7EC]"
                          : isToday(date)
                          ? "text-[#E4E7EC] bg-[#2E2E2E] border border-[#8A8A8A]"
                          : "text-[#BFBFBF] hover:bg-[#2E2E2E]"
                      }`}
                      aria-label={
                        isFutureDate(date)
                          ? `${date.toLocaleDateString()} - Future date not available`
                          : `Select ${date.toLocaleDateString()}`
                      }
                      title={
                        isFutureDate(date)
                          ? "Future dates are not available for recorded footage"
                          : undefined
                      }
                    >
                      {date.getDate()}
                      {isFutureDate(date) && (
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="w-6 h-0.5 bg-[#4A4A4A] transform rotate-45"></div>
                          <div className="w-6 h-0.5 bg-[#4A4A4A] transform -rotate-45 absolute"></div>
                        </div>
                      )}
                    </button>
                  )}
                </div>
              ))}
            </div>

            {/* Selected range display */}
            <div className="mb-6 p-3 bg-[#2E2E2E] rounded border border-[#3D3D3D]">
              <p className="text-[#8A8A8A] text-xs mb-1">Selected Range:</p>
              <p className="text-[#E4E7EC] text-sm">{formatDateRange()}</p>
            </div>

            {/* Action buttons */}
            <div className="flex gap-3 items-end">
              <button
                type="button"
                onClick={handleClear}
                className="flex-1 px-4 py-3 text-[#BFBFBF] border border-[#3D3D3D] rounded hover:bg-[#2E2E2E] transition-colors"
              >
                Clear
              </button>
              <div className="flex-1 flex justify-center">
                <SubmitButton
                  label="Enter"
                  type="button"
                  onClick={handleApply}
                  disabled={!startDate || !endDate}
                />
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default DateRangeModal;

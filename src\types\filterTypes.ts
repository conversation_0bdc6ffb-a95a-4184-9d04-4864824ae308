export interface FilterDropdownProps {
  label: string;
  options: string[];
  selectedOption: string;
  onSelect: (option: string) => void;
  onCustomClick?: () => void;
}

export interface MultiSelectSubDropdownProps {
  options: string[];
  selectedOptions: string[];
  onToggleOption: (option: string) => void;
  isVisible: boolean;
}

export interface SecondaryFilterTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  selectedCameras: string[];
  selectedLocations: string[];
  selectedTags: string[];
  onToggleCamera: (camera: string) => void;
  onToggleLocation: (location: string) => void;
  onToggleTag: (tag: string) => void;
}

export interface SearchInputProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  isSearchDropdownOpen: boolean;
  setIsSearchDropdownOpen: (open: boolean) => void;
  activeSecondaryTab: string;
  onReset: () => void;
}

export interface FilterControlsProps {
  selectedTimeRange: string;
  setSelectedTimeRange: (range: string) => void;
  selectedFaceRecognition: string;
  setSelectedFaceRecognition: (option: string) => void;
  selectedDateRange: { startDate: Date | null; endDate: Date | null };
  setSelectedDateRange: (range: { startDate: Date | null; endDate: Date | null }) => void;
  onDateRangeClick: () => void;
  selectedCustomTimeRange: { startTime: string | null; endTime: string | null };
  setSelectedCustomTimeRange: (range: { startTime: string | null; endTime: string | null }) => void;
  onTimeRangeClick: () => void;
}

export interface FootageSearchFilterBarProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  selectedTimeRange: string;
  setSelectedTimeRange: (range: string) => void;
  selectedFaceRecognition: string;
  setSelectedFaceRecognition: (option: string) => void;
  selectedDateRange: { startDate: Date | null; endDate: Date | null };
  setSelectedDateRange: (range: { startDate: Date | null; endDate: Date | null }) => void;
  activeSecondaryTab: string;
  setActiveSecondaryTab: (tab: string) => void;
  selectedCameras: string[];
  setSelectedCameras: (cameras: string[]) => void;
  selectedLocations: string[];
  setSelectedLocations: (locations: string[]) => void;
  selectedTags: string[];
  setSelectedTags: (tags: string[]) => void;
  selectedCustomTimeRange: { startTime: string | null; endTime: string | null };
  setSelectedCustomTimeRange: (range: { startTime: string | null; endTime: string | null }) => void;
}

export interface FilterTab {
  id: string;
  label: string;
  iconSrc?: string;
}

export interface DateRangeModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedDateRange: { startDate: Date | null; endDate: Date | null };
  onDateRangeSelect: (range: { startDate: Date | null; endDate: Date | null }) => void;
}

export interface TimeRangeModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedTimeRange: { startTime: string | null; endTime: string | null };
  onTimeRangeSelect: (range: { startTime: string | null; endTime: string | null }) => void;
}

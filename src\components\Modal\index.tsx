"use client";

import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { BlurredButton } from "@/components/button";

export const Modal = ({
  isOpen,
  onClose,
  children,
}: {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
}) => {
  const [contentKey, setContentKey] = useState(0);

  useEffect(() => {
    if (isOpen) {
      setContentKey((prev) => prev + 1);
    }
  }, [isOpen]);

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 bg-transparent flex items-center justify-center backdrop-blur-[6px] z-90"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
        >
          <motion.div
            className="bg-[#12121299] opacity-100 py-6 px-10 shadow-lg relative w-full max-w-xl mx-4 border border-solid backdrop-blur-md gradient-border"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 50 }}
            transition={{ type: "spring", mass: 1, stiffness: 96, damping: 6 }}
          >
            <BlurredButton onClick={onClose} className="absolute top-4 right-4">
              Cancel
            </BlurredButton>
            <motion.div
              key={contentKey}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
            >
              {children}
            </motion.div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

interface CamSetupModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  showCloseButton?: boolean;
}

export const CamSetupModal = ({
  isOpen,
  onClose,
  children,
  showCloseButton = true,
}: CamSetupModalProps) => {
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 bg-transparent flex items-center justify-center backdrop-blur-[6px] z-90"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
        >
          <motion.div
            className="bg-[#12121299] opacity-100 py-8 px-6 shadow-lg relative w-full max-w-xl mx-4 border border-solid backdrop-blur-md gradient-border"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
          >
            {showCloseButton && (
              <BlurredButton
                onClick={onClose}
                className="absolute top-4 right-4"
              >
                Cancel
              </BlurredButton>
            )}
            {children}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

interface DetailedModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  showCloseButton?: boolean;
}

export const DetailedModal = ({
  isOpen,
  onClose,
  children,
  showCloseButton = true,
}: DetailedModalProps) => {
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 bg-transparent flex items-center justify-center backdrop-blur-[6px] z-90"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
        >
          <motion.div
            className="bg-[#12121299] opacity-100 py-4 px-6 shadow-lg relative w-full max-w-xl mx-4 border border-solid backdrop-blur-md gradient-border"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
          >
            {showCloseButton && (
              <BlurredButton
                onClick={onClose}
                className="absolute top-4 right-4"
              >
                Cancel
              </BlurredButton>
            )}
            {children}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

interface AlertModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  preventOutsideClick?: boolean;
}

export const AlertModal = ({ 
  isOpen, 
  onClose, 
  children, 
  preventOutsideClick = false 
}: AlertModalProps) => {
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 bg-transparent flex items-center justify-center backdrop-blur-[2px] z-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          onClick={preventOutsideClick ? undefined : onClose}
        >
          <motion.div
            className="bg-[#121212] py-6 px-8 shadow-lg relative w-full max-w-7xl mx-4 backdrop-blur-md"
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            onClick={(e) => e.stopPropagation()}
          >
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3, delay: 0.1 }}
            >
              {children}
            </motion.div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

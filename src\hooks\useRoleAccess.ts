import { useAuthStore } from "@/store/authStore";
import { useState, useEffect } from "react";
import { authServices } from "@/utils/authServices";
import Cookies from "js-cookie";

export const useRoleAccess = () => {
  const { userRole, isAuthenticated, setUserRole } = useAuthStore();
  const [isLoading, setIsLoading] = useState(true);

  // Debug logging
  useEffect(() => {
    console.log("useRoleAccess - isAuthenticated:", isAuthenticated, "userRole:", userRole);
  }, [isAuthenticated, userRole]);

  // Fetch user role from backend if missing
  useEffect(() => {
    const fetchUserRole = async () => {
      if (isAuthenticated && !userRole) {
        try {
          const authToken = Cookies.get("authToken");
          if (authToken) {
            console.log("Fetching user role from backend...");
            const userData = await authServices.getUser(authToken);
            const role = userData.result?.role || userData.user?.role || userData.role;
            console.log("Fetched user role from backend:", role);
            if (role) {
              setUserRole(role);
            }
          }
        } catch (error: unknown) {
          const err = error as { response?: { status?: number } };
          console.error("Failed to fetch user role:", error);

          // Don't show toast errors for 403 (permission denied) when fetching user role
          // This is expected behavior when users don't have access to certain endpoints
          if (err.response?.status === 403) {
            console.log("Access denied (403) when fetching user role - this is expected for restricted endpoints");
          }

          // Security: Do NOT assume admin role for all users
          // Instead, leave role as null and let the access control handle it
          console.log("Failed to fetch user role - access will be denied for security");
          setUserRole(null);
        }
      }
    };

    fetchUserRole();
  }, [isAuthenticated, userRole, setUserRole]);

  // Handle loading state - stop loading when we have auth state or after timeout
  useEffect(() => {
    // If we have both auth status and role (or confirmed not authenticated), stop loading
    if ((isAuthenticated && userRole) || !isAuthenticated) {
      console.log("Stopping loading - auth state determined");
      setIsLoading(false);
      return;
    }

    // Otherwise, set a timeout to stop loading after a reasonable delay
    const timer = setTimeout(() => {
      console.log("Stopping loading - timeout reached");
      setIsLoading(false);
    }, 2000); // Give more time for API call to complete

    return () => clearTimeout(timer);
  }, [isAuthenticated, userRole]);

  const isAdmin = () => {
    return isAuthenticated && userRole === "admin";
  };

  const isUser = () => {
    return isAuthenticated && userRole === "user";
  };

  const isSecurity = () => {
    return isAuthenticated && userRole === "security";
  };

  const isAudit = () => {
    return isAuthenticated && userRole === "audit";
  };

  const hasRole = (role: string) => {
    return isAuthenticated && userRole === role;
  };

  const canAccessUserManagement = () => {
    return isAuthenticated; // Allow all authenticated users to access user management
  };

  return {
    userRole,
    isAdmin: isAdmin(),
    isUser: isUser(),
    isSecurity: isSecurity(),
    isAudit: isAudit(),
    hasRole,
    canAccessUserManagement: canAccessUserManagement(),
    isLoading,
  };
};

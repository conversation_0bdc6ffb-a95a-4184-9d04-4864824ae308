"use client";
import React from "react";

const SkeletonLoading = () => {
  return (
    <div className="relative flex min-h-screen bg-[#121212] text-white">
      {/* Placeholder for the background images */}
      <div className="absolute top-0 right-0 w-[70%] h-[60%] overflow-hidden animate-pulse">
        <div className="w-full h-full bg-[#2E2E2E] opacity-50"></div>
      </div>

      {/* Placeholder for the left-side image frame (visible on md screens) */}
      <div className="hidden md:flex w-full flex-col items-center justify-center p-4 relative z-10">
        <div className="relative 2xl:w-[60%] md:w-[78%] border border-[#3D3D3D] rounded-xl px-14 py-[3rem] animate-pulse">
          <div className="border-b border-t border-[#3D3D3D] py-[3rem] text-center">
            {/* Placeholder for TeletraanAuthFrame */}
            <div className="h-10 w-40 mx-auto bg-[#2E2E2E] rounded"></div>
            <div className="h-6 w-60 mx-auto mt-8 bg-[#2E2E2E] rounded"></div>
          </div>
        </div>
      </div>

      {/* Placeholder for the form section */}
      <div className="w-full relative flex flex-col justify-center p-4 md:p-6">
        <div className="w-full max-w-md md:max-w-lg 2xl:max-w-xl mx-auto animate-pulse">
          {/* Placeholder for the TELETRAAN title */}
          <div className="h-10 w-40 mx-auto mb-14 bg-[#2E2E2E] rounded"></div>

          {/* Placeholder for the tabs */}
          <div className="flex border border-[#3D3D3D] bg-[#1F1F1F] mb-6">
            <div className="h-12 w-1/2 bg-[#2E2E2E]"></div>
            <div className="h-12 w-1/2 bg-[#2E2E2E]"></div>
          </div>

          {/* Placeholder for the form fields */}
          <div className="space-y-4">
            {/* Email field */}
            <div className="h-16 w-full bg-[#2E2E2E] rounded"></div>
            {/* Password field */}
            <div className="h-16 w-full bg-[#2E2E2E] rounded"></div>
            {/* Confirm Password field (for Sign Up) or Forgot Password link (for Sign In) */}
            <div className="h-6 w-1/2 bg-[#2E2E2E] rounded"></div>
            {/* Submit button */}
            <div className="h-12 w-40 mx-auto bg-[#2E2E2E] rounded"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SkeletonLoading;

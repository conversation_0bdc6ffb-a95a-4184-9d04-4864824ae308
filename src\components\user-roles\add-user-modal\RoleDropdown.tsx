"use client";
import React from "react";
import { IoChevronDown } from "react-icons/io5";

interface RoleDropdownProps {
  selectedRole: string;
  isOpen: boolean;
  onToggle: () => void;
  onRoleSelect: (role: string) => void;
  error?: string;
  isFocused?: string | null;
  submitAttempted?: boolean;
}

const ROLE_OPTIONS = ["Admin", "User", "Security", "Audit"];

const RoleDropdown: React.FC<RoleDropdownProps> = ({
  selectedRole,
  isOpen,
  onToggle,
  onRoleSelect,
  error,
  isFocused,
  submitAttempted,
}) => {
  return (
    <div>
      <label className="block text-sm text-[#E4E7EC] mb-1">
        Select Role
      </label>
      <div className="relative">
        <div
          className={`w-full bg-[#1F1F1F] text-white py-3 px-4 cursor-pointer flex items-center justify-between focus:outline-none focus:ring-1 ${
            error && (isFocused === "role" || submitAttempted)
              ? "border border-red-500 focus:ring-red-500"
              : "focus:ring-[#E4E7EC]"
          }`}
          onClick={onToggle}
        >
          <span className={selectedRole ? "text-white" : "text-[#8A8A8A]"}>
            {selectedRole || "Choose Role"}
          </span>
          <IoChevronDown
            size={16}
            className={`transition-transform ${isOpen ? "rotate-180" : ""}`}
          />
        </div>
        
        {isOpen && (
          <div className="absolute top-full left-0 right-0 bg-[#2E2E2E] border border-[#3D3D3D] z-10 max-h-48 overflow-y-auto">
            {ROLE_OPTIONS.map((role) => (
              <div
                key={role}
                className="px-4 py-3 cursor-pointer text-sm text-[#E4E7EC] hover:bg-[#3D3D3D] transition-colors"
                onClick={() => onRoleSelect(role)}
              >
                {role}
              </div>
            ))}
          </div>
        )}
      </div>
      {error && (isFocused === "role" || submitAttempted) && (
        <p className="text-red-500 text-xs mt-1 error-message">{error}</p>
      )}
    </div>
  );
};

export default RoleDropdown;

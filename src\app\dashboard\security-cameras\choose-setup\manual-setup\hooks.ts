import { useState } from "react";
import { getInputType } from "@/utils/rtspUtils";

type ManualFormData = {
  ipAddress: string;
  location: string;
};

export const useManualSetupForm = (
  onSubmit: (formData: ManualFormData) => void
) => {
  const [formData, setFormData] = useState<ManualFormData>({
    ipAddress: "",
    location: "",
  });
  const [errors, setErrors] = useState<Record<keyof ManualFormData, string>>({
    ipAddress: "",
    location: "",
  });
  const [focusedField, setFocusedField] = useState<keyof ManualFormData | null>(
    null
  );
  const [submitAttempted, setSubmitAttempted] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [id]: value,
    }));

    if (id === "ipAddress") {
      const validationError = validateIpAddress(value);
      setErrors((prev) => ({
        ...prev,
        [id]: validationError,
      }));
    } else if (id === "location") {
      setErrors((prev) => ({
        ...prev,
        [id]: "",
      }));
    }
  };

  const validateIpAddress = (value: string): string => {
    const trimmedValue = value.trim();

    if (!trimmedValue) {
      return "";
    }

    const inputType = getInputType(trimmedValue);

    if (inputType === 'invalid') {
      return "Please enter a valid IP address (e.g., *************) or camera ID (alphanumeric characters, hyphens, underscores only)";
    }

    return "";
  };

  const validateForm = (): boolean => {
    const newErrors: Record<keyof ManualFormData, string> = {
      ipAddress: "",
      location: "",
    };

    if (!formData.ipAddress.trim()) {
      newErrors.ipAddress = "Camera ID/IP Address is required";
    } else {
      newErrors.ipAddress = validateIpAddress(formData.ipAddress);
    }

    if (!formData.location.trim()) {
      newErrors.location = "Location is required";
    }

    setErrors(newErrors);
    return !Object.values(newErrors).some((error) => error);
  };

  const handleSubmit = () => {
    setSubmitAttempted(true);
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  const handleFocus = (field: keyof ManualFormData) => {
    setFocusedField(field);
  };

  const handleBlur = () => {
    setFocusedField(null);
  };

  return {
    formData,
    errors,
    focusedField,
    submitAttempted,
    handleChange,
    handleSubmit,
    handleFocus,
    handleBlur,
  };
};
"use client";
import React from "react";
import { BlurredButton } from "@/components/button";
import Image from "next/image";
import { HiArrowLeft } from "react-icons/hi";
import { HiDownload } from "react-icons/hi";
import { useDetailedFootageLogic } from "./hooks";

interface PageProps {
  params: Promise<{ id: string }>;
  searchParams?: Promise<{ [key: string]: string | string[] | undefined }>;
}

export default function DetailedFootagePage({
  // eslint-disable-next-line
  params: _,
}: PageProps) {
  const {
    selectedFootage,
    loading,
    handleBack,
    handleDownload,
    formatDateTime,
  } = useDetailedFootageLogic();

  if (loading) {
    return <div className="text-white text-center p-4 lg:p-6">Loading...</div>;
  }

  if (!selectedFootage) {
    return (
      <div className="text-white text-center p-4 lg:p-6">Footage not found</div>
    );
  }

  return (
    <div className="text-white py-4 lg:p-6 min-h-screen">
      <div className="mb-3 lg:mb-4">
        <BlurredButton
          className="flex items-center gap-2 cursor-pointer bg-[#1F1F1F] text-white text-xs lg:text-sm border border-solid px-3 py-1 lg:px-5 lg:py-2 gradient-border"
          onClick={handleBack}
        >
          <HiArrowLeft />
          All Footage
        </BlurredButton>
      </div>

      <div className="mb-4 lg:mb-5">
        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center mb-2 gap-3 lg:gap-0">
          <div className="flex flex-col lg:flex-row lg:items-center gap-2">
            <p className="text-lg lg:text-xl text-white">
              {selectedFootage.cameraName}
            </p>
          </div>
          <div className="flex flex-col lg:flex-row gap-2 lg:gap-3">
            <div
              className="flex cursor-pointer items-center px-4 py-1 lg:px-5 lg:py-2 bg-[#3D3D3D] text-[#BFBFBF] font-medium text-xs lg:text-sm"
              onClick={handleDownload}
            >
              <HiDownload size={16} className="mr-2" />
              Download Footage
            </div>
          </div>
        </div>

        <div className="flex items-center text-[#8A8A8A] text-xs">
          <Image
            src="/location.svg"
            alt="Location Icon"
            width={14}
            height={14}
            className="mr-1"
          />
          {selectedFootage.location}
        </div>
      </div>

      <div className="relative mb-4 lg:mb-6 border-b border-[#3D3D3D] pb-6 lg:pb-9">
        <div className="flex h-[300px] lg:h-[600px] bg-gray-900 rounded-lg overflow-hidden">
          <video
            className="w-full h-full object-contain"
            controls
            autoPlay
            poster={selectedFootage.thumbnailUrl}
          >
            <source src={selectedFootage.videoUrl} type="video/mp4" />
            Your browser does not support the video tag.
          </video>

          {/* Timestamp overlay */}
          <div className="absolute bottom-4 right-4 bg-black bg-opacity-75 text-white px-3 py-1 rounded text-sm">
            {formatDateTime(selectedFootage.startTime)}
          </div>
        </div>
      </div>
    </div>
  );
}

"use client";
import React from "react";
import { DetailedModal } from "@/components/Modal";
import { Camera } from "@/lib/cameras";
import { useRouter } from "next/navigation";
import { useCameras } from "@/hooks/useCameras";

interface DisconnectCameraModalProps {
  isOpen: boolean;
  onClose: () => void;
  camera: Camera | null;
  refreshCameras?: () => void;
}

const DisconnectCameraModal: React.FC<DisconnectCameraModalProps> = ({
  isOpen,
  onClose,
  camera,
  refreshCameras,
}) => {
  const router = useRouter();
  const { removeCamera } = useCameras();

  const handleDisconnect = () => {
    if (!camera) return;

    removeCamera(camera.id);
    console.log("[DisconnectCameraModal] Removed camera:", camera.id);

    if (refreshCameras) {
      refreshCameras();
    }

    onClose();
    router.push("/dashboard/security-cameras");
  };

  return (
    <DetailedModal showCloseButton={false} isOpen={isOpen} onClose={onClose}>
      <div className="mb-10">
        <h2 className="text-lg lg:text-xl text-white font-medium pb-4">
          Are you sure you want to Disconnect Camera?
        </h2>
        <p className="text-[#A3A3A3] text-sm lg:text-base mb-4">
          This action will permanently remove the{" "}
          <b className="text-white">{camera?.name} </b> camera from your system.
          You&apos;ll need to go through setup again if you wish to reconnect it
          later.{" "}
        </p>
      </div>
      <div className="flex justify-end space-x-4">
        <button
          className="bg-[#1F1F1F] cursor-pointer font-medium shadow-[0px_0px_4px_0px_#242424_inset,_4px_4px_4px_0px_#00000040] border border-[#262626] text-white px-4 py-2 transition-colors"
          onClick={onClose}
        >
          Cancel
        </button>
        <button
          className="bg-[#F04438] text-black px-4 py-2"
          onClick={handleDisconnect}
        >
          Disconnect Camera
        </button>
      </div>
    </DetailedModal>
  );
};

export default DisconnectCameraModal;

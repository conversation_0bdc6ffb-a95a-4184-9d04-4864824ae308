export interface FormData {
  email: string;
  password: string;
  confirmPassword: string;
  name: string;
  companyName: string;
  forgotEmail?: string;
  resetPassword?: string;
  token?: string;
  userId?: string;
}

export interface Errors {
  email: string;
  password: string;
  confirmPassword: string;
  name: string;
  companyName: string;
  forgotEmail?: string;
  resetPassword?: string;
}

export interface InvitationData {
  email: string;
  role: string;
  invitedBy: string;
  organizationName: string;
  message: string;
  expiresAt: string;
}

export interface AuthStore {
  activeTab: "Sign Up" | "Sign In";
  setActiveTab: (tab: "Sign Up" | "Sign In") => void;
  showPassword: boolean;
  setShowPassword: (value: boolean) => void;
  showConfirmPassword: boolean;
  setShowConfirmPassword: (value: boolean) => void;
  formData: FormData;
  setFormData: (data: Partial<FormData>) => void;
  errors: Errors;
  setErrors: (errors: Errors | ((prevErrors: Errors) => Errors)) => void;
  focusedField: keyof FormData | null;
  setFocusedField: (field: keyof FormData | null) => void;

  // Forgot Password states
  forgotPasswordStep: number;
  setForgotPasswordStep: (step: number) => void;
  otp: string[];
  setOtp: (otp: string[]) => void;
  otpSent: boolean;
  setOtpSent: (value: boolean) => void;
  otpError: string;
  setOtpError: (error: string) => void;

  // Submit attempt tracking
  submitAttempted: boolean;
  setSubmitAttempted: (value: boolean) => void;

  // Authentication status
  isAuthenticated: boolean;
  setIsAuthenticated: (value: boolean) => void;
  clearAuth: () => void;

  // API response data
  token: string | null;
  setToken: (token: string | null) => void;
  userId: string | null;
  setUserId: (userId: string | null) => void;
  userRole: string | null;
  setUserRole: (role: string | null) => void;

  // Invitation data
  invitationData: InvitationData | null;
  setInvitationData: (data: InvitationData | null) => void;
}

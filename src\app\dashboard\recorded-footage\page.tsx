"use client";
import React, { useState, useMemo } from "react";
import Image from "next/image";
import { HiOutlineArrowRight } from "react-icons/hi";
import { IconButton } from "@/components/button";
import RecordedFootageGrid from "@/components/recorded-footage/RecordedFootageGrid";
import { mockRecordedFootage, type RecordedFootage } from "@/lib/recordedFootage";

export default function RecordedFootage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTimeRange, setSelectedTimeRange] = useState("");
  const [selectedCamera] = useState("All Cameras");
  const [selectedFaceRecognition, setSelectedFaceRecognition] = useState("");
  const [selectedDateRange, setSelectedDateRange] = useState<{ startDate: Date | null; endDate: Date | null }>({
    startDate: null,
    endDate: null,
  });
  const [activeSecondaryTab, setActiveSecondaryTab] = useState("all");
  const [selectedCameras, setSelectedCameras] = useState<string[]>([]);
  const [selectedLocations, setSelectedLocations] = useState<string[]>([]);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedCustomTimeRange, setSelectedCustomTimeRange] = useState<{ startTime: string | null; endTime: string | null }>({
    startTime: null,
    endTime: null,
  });

  const filteredFootage = useMemo(() => {
    return mockRecordedFootage.filter((footage: RecordedFootage) => {
      const matchesSearch =
        footage.cameraName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        footage.location.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesCamera = selectedCamera === "All Cameras" || footage.cameraName === selectedCamera;

      // Date range filtering
      const matchesDateRange = (() => {
        if (!selectedDateRange.startDate && !selectedDateRange.endDate) {
          return true; // No date filter applied
        }

        const footageDate = new Date(footage.startTime);
        const startOfDay = (date: Date) => new Date(date.getFullYear(), date.getMonth(), date.getDate());
        const endOfDay = (date: Date) => new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999);

        if (selectedDateRange.startDate && selectedDateRange.endDate) {
          const startDate = startOfDay(selectedDateRange.startDate);
          const endDate = endOfDay(selectedDateRange.endDate);
          return footageDate >= startDate && footageDate <= endDate;
        } else if (selectedDateRange.startDate) {
          const startDate = startOfDay(selectedDateRange.startDate);
          return footageDate >= startDate;
        } else if (selectedDateRange.endDate) {
          const endDate = endOfDay(selectedDateRange.endDate);
          return footageDate <= endDate;
        }

        return true;
      })();

      return matchesSearch && matchesCamera && matchesDateRange;
    });
  }, [searchTerm, selectedCamera, selectedDateRange]);

  const locations = Array.from(new Set(mockRecordedFootage.map((f: RecordedFootage) => f.location)));

  return (
    <div className="flex flex-col text-white lg:p-6 py-6">
      {filteredFootage.length === 0 && searchTerm === "" && selectedCamera === "All Cameras" ? (
        <div className="max-w-md mt-28 w-full flex flex-col items-center text-center mx-auto">
          <div className="mb-6">
            <Image
              src="/rf.svg"
              alt="Recorded Footage"
              width={160}
              height={160}
              className="w-40 h-40 sm:w-56 sm:h-56"
              priority
            />
          </div>

          <div className="text-[#E4E7EC]">
            <p className="text-sm sm:text-base">No recorded footage available yet.</p>
            <p className="text-sm sm:text-base">
              Footage will appear here once cameras start recording
            </p>
          </div>
          <div className="mt-3">
            <IconButton
              label="View Cameras"
              icon={HiOutlineArrowRight}
              onClick={() => window.location.href = '/dashboard/security-cameras'}
            />
          </div>
        </div>
      ) : (
        <div className="">
          <RecordedFootageGrid
            footage={filteredFootage}
            locations={locations}
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            selectedTimeRange={selectedTimeRange}
            setSelectedTimeRange={setSelectedTimeRange}
            selectedFaceRecognition={selectedFaceRecognition}
            setSelectedFaceRecognition={setSelectedFaceRecognition}
            selectedDateRange={selectedDateRange}
            setSelectedDateRange={setSelectedDateRange}
            activeSecondaryTab={activeSecondaryTab}
            setActiveSecondaryTab={setActiveSecondaryTab}
            selectedCameras={selectedCameras}
            setSelectedCameras={setSelectedCameras}
            selectedLocations={selectedLocations}
            setSelectedLocations={setSelectedLocations}
            selectedTags={selectedTags}
            setSelectedTags={setSelectedTags}
            selectedCustomTimeRange={selectedCustomTimeRange}
            setSelectedCustomTimeRange={setSelectedCustomTimeRange}
          />
        </div>
      )}
    </div>
  );
}
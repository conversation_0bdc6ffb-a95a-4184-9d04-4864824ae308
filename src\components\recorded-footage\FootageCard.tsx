"use client";
import React from "react";
import Image from "next/image";
import { RecordedFootage } from "@/lib/recordedFootage";

interface FootageCardProps {
  footage: RecordedFootage;
  onClick: () => void;
}

const FootageCard: React.FC<FootageCardProps> = ({ footage, onClick }) => {
  return (
    <div
      onClick={onClick}
      className="relative cursor-pointer group overflow-hidden"
      style={{
        minHeight: '280px'
      }}
    >
      <div className="relative h-64 cursor-pointer overflow-hidden bg-gray-900">
        <Image
          width={400}
          height={256}
          src={footage.thumbnailUrl}
          alt={`${footage.cameraName} footage`}
          className="object-cover w-full h-full"
        />



        {/* Date overlay - top left */}
        <div
          className="absolute top-2 left-2 text-white px-2 py-1 rounded"
          style={{
            background: 'linear-gradient(145.42deg, #1F1F1F 32.16%, #707070 237.44%)',
            fontSize: '10px'
          }}
        >
          Apr, 08.2025
        </div>

        {/* Time range overlay - top right */}
        <div
          className="absolute top-2 right-2 text-white px-2 py-1 rounded"
          style={{
            background: 'linear-gradient(145.42deg, #1F1F1F 32.16%, #707070 237.44%)',
            fontSize: '10px'
          }}
        >
          08:30:02 - 8:45:00am
        </div>
      </div>
    </div>
  );
};

export default FootageCard;

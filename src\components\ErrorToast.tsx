import { toast } from "react-hot-toast";
import { ImCross } from "react-icons/im";

interface ErrorToastProps {
  message: string;
  errors: string[];
}

export const ErrorToast = ({ message, errors }: ErrorToastProps) => {
  const displayMessage = typeof message === 'string' ? message : 'An error occurred';
  const displayErrors = Array.isArray(errors)
    ? errors.filter(error => typeof error === 'string' && error.trim() !== '')
    : [];

  return (
    <div className="p-4 rounded-lg bg-[#1F1F1F] border border-solid border-[#3D3D3D] text-[#E4E7EC] shadow-[0_3px_10px_rgba(0,0,0,0.1),0_3px_3px_rgba(0,0,0,0.05)] flex items-center">
      <span className="flex-shrink-0 w-6 h-6 leading-[1px] rounded-full bg-red-600 flex items-center justify-center mr-2 text-white text-xs">
        <ImCross />
      </span>
      <div className="flex-1">
        <p className="text-base font-medium leading-normal">{displayMessage}</p>
        {displayErrors.length > 0 && (
          <ul className="list-disc list-inside mt-2 text-sm leading-normal">
            {displayErrors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
};

export const showDetailedErrorToast = (message: unknown, errors: unknown[]) => {
  const safeMessage = typeof message === 'string' ? message : 'An error occurred';
  const safeErrors = Array.isArray(errors)
    ? errors.map(error => typeof error === 'string' ? error : String(error)).filter(error => error.trim() !== '')
    : [];

  toast.custom(() => <ErrorToast message={safeMessage} errors={safeErrors} />, {
    duration: 5000,
    position: "top-right",
  });
};

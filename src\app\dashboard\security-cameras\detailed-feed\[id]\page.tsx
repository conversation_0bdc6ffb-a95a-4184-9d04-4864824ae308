"use client";
import React from "react";
import { BlurredButton } from "@/components/button/";
import { HiArrowLeft } from "react-icons/hi";
import { RiEqualizerLine } from "react-icons/ri";
import { MdOutlineArrowOutward } from "react-icons/md";
import Image from "next/image";
import { useDetailedFeedLogic } from "./hooks";
import EditCameraModal from "@/components/sec-camera-comps/detailed-feed/edit-camera";
import DisconnectCameraModal from "@/components/sec-camera-comps/detailed-feed/disconnect-camera";
import { useCameras } from "@/hooks/useCameras";
import RTSPVideoPlayer from "@/components/RTSPVideoPlayer";
import NoCameraFound from "@/components/NoCameraFound";
import { Camera } from "@/lib/cameras";

interface PageProps {
  params: Promise<{ id: string }>;
  searchParams?: Promise<{ [key: string]: string | string[] | undefined }>;
}

export default function DetailedFeedPage({
  // eslint-disable-next-line
  params: _
}: PageProps) {
  const { refreshCameras } = useCameras();
  const {
    selectedCamera,
    setSelectedCamera,
    groupedAlerts,
    loading,
    formatDate,
    isToday,
    getAlertIcon,
    handleBack,
    handleEditCamera,
    handleDisconnectCamera,
    isEditModalOpen,
    setIsEditModalOpen,
    isDisconnectModalOpen,
    setIsDisconnectModalOpen,
  } = useDetailedFeedLogic();

  const handleUpdateCamera = (updatedCamera: Camera) => {
    console.log("[DetailedFeed] Received updated camera:", updatedCamera);
    if (!setSelectedCamera) {
      console.error("[DetailedFeed] setSelectedCamera is not defined");
      return;
    }
    if (selectedCamera && updatedCamera.id === selectedCamera.id) {
      setSelectedCamera(updatedCamera);
      console.log("[DetailedFeed] Updated selectedCamera state");
    }
  };

  if (loading) {
    return <div className="text-white text-center p-4 lg:p-6">Loading...</div>;
  }

  if (!selectedCamera) {
    return (
      <div className="text-white text-center p-4 lg:p-6">Camera not found</div>
    );
  }

  return (
    <div className="text-white py-4 lg:p-6 min-h-screen">
      <div className="mb-3 lg:mb-4">
        <BlurredButton
          className="flex items-center gap-2 cursor-pointer bg-[#1F1F1F] text-white text-xs lg:text-sm border border-solid px-3 py-1 lg:px-5 lg:py-2 gradient-border"
          onClick={handleBack}
        >
          <HiArrowLeft />
          All Cameras
        </BlurredButton>
      </div>

      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center mb-4 lg:mb-5 gap-3 lg:gap-0">
        <div className="flex flex-col lg:flex-row lg:items-center gap-2">
          <p className="text-lg lg:text-xl text-white">{selectedCamera.name}</p>
          <div className="w-[60px] flex cursor-pointer justify-center items-center py-1 border-[0.5px] border-[#8A8A8A] text-[#8A8A8A] text-xs">
            <div className="w-2 h-2 bg-[#8A8A8A] rounded-full mr-1" />
            LIVE
          </div>
        </div>
        <div className="flex flex-col lg:flex-row gap-2 lg:gap-3">
          <div
            className="flex cursor-pointer items-center px-4 py-1 lg:px-5 lg:py-2 bg-[#3D3D3D] text-[#BFBFBF] font-medium text-xs lg:text-sm"
            onClick={handleEditCamera}
          >
            <RiEqualizerLine size={16} className="mr-2" />
            Edit Camera
          </div>
          <div
            className="flex cursor-pointer items-center px-4 py-1 lg:px-5 lg:py-2 border border-[#F04438] text-[#F04438] font-medium text-xs lg:text-sm"
            onClick={handleDisconnectCamera}
          >
            Disconnect Camera
          </div>
        </div>
      </div>

      <div className="relative mb-4 lg:mb-6 border-b border-[#3D3D3D] pb-6 lg:pb-9">
        <div className="flex h-[300px] lg:h-[600px] bg-gray-900 rounded-lg overflow-hidden">
          {selectedCamera.url ? (
            <RTSPVideoPlayer
              camera={selectedCamera}
              className="w-full h-full"
              onError={(error) => console.error("Stream error:", error)}
            />
          ) : (
            <NoCameraFound
              ipAddress={selectedCamera.name}
              className="w-full h-full"
            />
          )}
        </div>
      </div>

      <div className="mb-4 lg:mb-6">
        <h2 className="text-base lg:text-lg font-medium mb-3 lg:mb-4">
          Alert History
        </h2>
        <div className="w-full lg:w-[85%] bg-[#1F1F1F] border border-[#2E2E2E] p-3 lg:p-4">
          {Object.entries(groupedAlerts).map(
            ([date, dateAlerts], dateIndex) => (
              <div key={dateIndex} className="mb-3 lg:mb-4">
                <p className="text-xs lg:text-sm text-[#8A8A8A] mb-1 lg:mb-2">
                  {isToday(date) ? "Today" : formatDate(date)}
                </p>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 lg:gap-4 border-b border-[#3D3D3D] pb-4 lg:pb-5">
                  {dateAlerts.map((alert, index) => (
                    <div
                      key={index}
                      className="p-3 lg:p-4 border border-solid grade flex flex-col lg:flex-row lg:items-center lg:justify-between gap-2 lg:gap-0"
                    >
                      <div className="flex items-center gap-2">
                        <div className="flex items-center justify-center bg-[#3D3D3D] p-2 lg:p-3">
                          <Image
                            src={getAlertIcon(alert.type)}
                            alt={`${alert.type} Icon`}
                            width={24}
                            height={24}
                            className="w-4 h-4 lg:w-5 lg:h-5 mb-1"
                          />
                        </div>
                        <div>
                          <p className="text-xs lg:text-sm text-white">
                            {alert.type}
                          </p>
                          <p className="text-[10px] lg:text-xs text-[#8A8A8A] mt-1 truncate">
                            {alert.time}
                            {alert.details ? ` - ${alert.details}` : ""}
                          </p>
                        </div>
                      </div>
                      <button
                        type="button"
                        className="text-[#A3A3A3] cursor-pointer text-[10px] lg:text-xs flex items-center gap-1 self-start lg:self-center"
                      >
                        View Details
                        <span className="pb-1">
                          <MdOutlineArrowOutward size={16} />
                        </span>
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )
          )}
        </div>
      </div>

      <EditCameraModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        camera={selectedCamera}
        onUpdate={handleUpdateCamera}
        refreshCameras={refreshCameras}
      />
      <DisconnectCameraModal
        isOpen={isDisconnectModalOpen}
        onClose={() => setIsDisconnectModalOpen(false)}
        camera={selectedCamera}
        refreshCameras={refreshCameras}
      />
    </div>
  );
}
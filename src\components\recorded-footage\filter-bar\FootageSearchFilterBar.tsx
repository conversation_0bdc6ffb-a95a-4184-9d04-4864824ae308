"use client";
import React, { useState } from "react";
import SearchInput from "./SearchInput";
import FilterControls from "./FilterControls";
import SecondaryFilterTabs from "./SecondaryFilterTabs";
import DateRangeModal from "./DateRangeModal";
import TimeRangeModal from "./TimeRangeModal";
import { FootageSearchFilterBarProps } from "@/types/filterTypes";

const FootageSearchFilterBar: React.FC<FootageSearchFilterBarProps> = ({
  searchTerm,
  setSearchTerm,
  selectedTimeRange,
  setSelectedTimeRange,
  selectedFaceRecognition,
  setSelectedFaceRecognition,
  selectedDateRange,
  setSelectedDateRange,
  activeSecondaryTab,
  setActiveSecondaryTab,
  selectedCameras,
  setSelectedCameras,
  selectedLocations,
  setSelectedLocations,
  selectedTags,
  setSelectedTags,
  selectedCustomTimeRange,
  setSelectedCustomTimeRange,
}) => {
  const [isSearchDropdownOpen, setIsSearchDropdownOpen] = useState(false);
  const [isDateRangeModalOpen, setIsDateRangeModalOpen] = useState(false);
  const [isTimeRangeModalOpen, setIsTimeRangeModalOpen] = useState(false);

  const handleToggleCamera = (camera: string) => {
    const newSelection = selectedCameras.includes(camera)
      ? selectedCameras.filter(c => c !== camera)
      : [...selectedCameras, camera];
    setSelectedCameras(newSelection);
    updateSearchTerm(newSelection, selectedLocations, selectedTags);
  };

  const handleToggleLocation = (location: string) => {
    const newSelection = selectedLocations.includes(location)
      ? selectedLocations.filter(l => l !== location)
      : [...selectedLocations, location];
    setSelectedLocations(newSelection);
    updateSearchTerm(selectedCameras, newSelection, selectedTags);
  };

  const handleToggleTag = (tag: string) => {
    const newSelection = selectedTags.includes(tag)
      ? selectedTags.filter(t => t !== tag)
      : [...selectedTags, tag];
    setSelectedTags(newSelection);
    updateSearchTerm(selectedCameras, selectedLocations, newSelection);
  };

  const handleReset = () => {
    setSelectedCameras([]);
    setSelectedLocations([]);
    setSelectedTags([]);
    setSearchTerm("");
    setActiveSecondaryTab("all");
    setIsSearchDropdownOpen(false);
  };

  const updateSearchTerm = (cameras: string[], locations: string[], tags: string[]) => {
    const searchParts: string[] = [];

    if (cameras.length > 0) {
      searchParts.push(...cameras);
    }
    if (locations.length > 0) {
      searchParts.push(...locations);
    }
    if (tags.length > 0) {
      searchParts.push(...tags);
    }

    setSearchTerm(searchParts.join(", "));
  };

  const handleTabChange = (tab: string) => {
    setActiveSecondaryTab(tab);
    if (tab === "all") {
      handleReset();
      setIsSearchDropdownOpen(false);
    }
  };

  const handleDateRangeClick = () => {
    setIsDateRangeModalOpen(true);
  };

  const handleDateRangeSelect = (range: { startDate: Date | null; endDate: Date | null }) => {
    setSelectedDateRange(range);
  };

  const handleTimeRangeClick = () => {
    setIsTimeRangeModalOpen(true);
  };

  const handleTimeRangeSelect = (range: { startTime: string | null; endTime: string | null }) => {
    setSelectedCustomTimeRange(range);
  };

  return (
    <div className="mb-8 relative">
      {/* Main search and filters row */}
      <div className="lg:flex items-center justify-between">
        {/* Search bar with integrated dropdown */}
        <SearchInput
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          isSearchDropdownOpen={isSearchDropdownOpen}
          setIsSearchDropdownOpen={setIsSearchDropdownOpen}
          activeSecondaryTab={activeSecondaryTab}
          onReset={handleReset}
        />

        {/* Filters label and filter dropdowns */}
        <FilterControls
          selectedTimeRange={selectedTimeRange}
          setSelectedTimeRange={setSelectedTimeRange}
          selectedFaceRecognition={selectedFaceRecognition}
          setSelectedFaceRecognition={setSelectedFaceRecognition}
          selectedDateRange={selectedDateRange}
          setSelectedDateRange={setSelectedDateRange}
          onDateRangeClick={handleDateRangeClick}
          selectedCustomTimeRange={selectedCustomTimeRange}
          setSelectedCustomTimeRange={setSelectedCustomTimeRange}
          onTimeRangeClick={handleTimeRangeClick}
        />
      </div>

      {/* Secondary filter tabs - shown when search dropdown is open */}
      {isSearchDropdownOpen && (
        <div className="absolute top-full left-0 right-0 z-50 shadow-lg">
          <SecondaryFilterTabs
            activeTab={activeSecondaryTab}
            onTabChange={handleTabChange}
            selectedCameras={selectedCameras}
            selectedLocations={selectedLocations}
            selectedTags={selectedTags}
            onToggleCamera={handleToggleCamera}
            onToggleLocation={handleToggleLocation}
            onToggleTag={handleToggleTag}
          />
        </div>
      )}

      {/* Date Range Modal */}
      <DateRangeModal
        isOpen={isDateRangeModalOpen}
        onClose={() => setIsDateRangeModalOpen(false)}
        selectedDateRange={selectedDateRange}
        onDateRangeSelect={handleDateRangeSelect}
      />

      {/* Time Range Modal */}
      <TimeRangeModal
        isOpen={isTimeRangeModalOpen}
        onClose={() => setIsTimeRangeModalOpen(false)}
        selectedTimeRange={selectedCustomTimeRange}
        onTimeRangeSelect={handleTimeRangeSelect}
      />
    </div>
  );
};

export default FootageSearchFilterBar;

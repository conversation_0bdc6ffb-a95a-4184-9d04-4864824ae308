"use client";
import React from "react";
import { IoCheckmark } from "react-icons/io5";
import { MultiSelectSubDropdownProps } from "@/types/filterTypes";

const MultiSelectSubDropdown: React.FC<MultiSelectSubDropdownProps> = ({
  options,
  selectedOptions,
  onToggleOption,
  isVisible,
}) => {
  if (!isVisible) return null;

  return (
    <div className="bg-[#1F1F1F] border-t-[0.1px] p-4">
      <div className="grid grid-cols-3 gap-3 mb-4">
        {options.map((option) => (
          <div
            key={option}
            className={`flex items-center justify-between px-3 py-2 cursor-pointer transition-colors ${
              selectedOptions.includes(option)
                ? "bg-[#2E2E2E] text-[#A3A3A3] text-xs gradient-border"
                : "bg-[#3D3D3D] text-[#A3A3A3] text-xs"
            }`}
            onClick={() => onToggleOption(option)}
          >
            <span>{option}</span>
            {selectedOptions.includes(option) && (
              <IoCheckmark size={14} className="text-[#A3A3A3]" />
            )}
          </div>
        ))}
      </div>
      <div className="flex justify-end">
        <button
          type="button"
          className="px-6 py-2 bg-[#3D3D3D] text-[#BFBFBF] text-xs hover:bg-[#4D4D4D] transition-colors"
        >
          Search
        </button>
      </div>
    </div>
  );
};

export default MultiSelectSubDropdown;

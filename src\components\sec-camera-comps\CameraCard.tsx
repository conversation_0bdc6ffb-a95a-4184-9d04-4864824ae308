"use client";
import React, { useState } from "react";
import Image from "next/image";
import { Camera } from "@/lib/cameras";
import RTSPVideoPlayer from "@/components/RTSPVideoPlayer";
import NoCameraFound from "@/components/NoCameraFound";

interface CameraCardProps {
  camera: Camera;
  onClick: () => void;
}

const CameraCard: React.FC<CameraCardProps> = ({ camera, onClick }) => {
  const [streamError, setStreamError] = useState(false);

  const handleStreamError = (error: string) => {
    console.error(`Stream error for camera ${camera.name}:`, error);
    setStreamError(true);
  };

  const handleStreamLoad = () => {
    setStreamError(false);
  };

  const handleRetry = () => {
    setStreamError(false);
    window.location.reload();
  };

  return (
    <div onClick={onClick} className="relative cursor-pointer group">
      <div className="aspect-video bg-gray-900 rounded-lg overflow-hidden">
        {camera.url && !streamError ? (
          <RTSPVideoPlayer
            camera={camera}
            className="w-full h-full"
            onError={handleStreamError}
            onStreamLoad={handleStreamLoad}
          />
        ) : streamError ? (
          <NoCameraFound
            ipAddress={camera.url || "Unknown"}
            onRetry={handleRetry}
            className="w-full h-full bg-gray-900"
          />
        ) : (
          <div className="relative w-full h-full">
            <Image
              width={600}
              height={350}
              src={camera.image || "/videos/office-lobby.gif"}
              alt={camera.name}
              className="object-cover w-full h-full"
            />
            <div className="absolute top-2 left-2 bg-gray-600 text-white px-2 py-1 rounded text-xs">
              OFFLINE
            </div>
          </div>
        )}
      </div>

      <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white px-3 py-1 rounded text-sm">
        {camera.name}
      </div>

      <div className={`absolute top-2 right-2 w-3 h-3 rounded-full ${
        camera.connectionStatus === 'online' ? 'bg-green-500' :
        camera.connectionStatus === 'offline' ? 'bg-red-500' : 'bg-yellow-500'
      }`} />
    </div>
  );
};

export default CameraCard;

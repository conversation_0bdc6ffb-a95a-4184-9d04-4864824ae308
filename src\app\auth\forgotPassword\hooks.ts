import { useRouter } from "next/navigation";
import { useAuthStore } from "@/store/authStore";
import { authServices } from "@/utils/authServices";
import { showDetailedErrorToast } from "@/components/ErrorToast";

export const useForgotPassword = () => {
  const router = useRouter();
  const {
    formData,
    setFormData,
    errors,
    setErrors,
    forgotPasswordStep,
    setForgotPasswordStep,
    otp,
    setOtp,
    otpSent,
    setOtpSent,
    otpError,
    setOtpError,
  } = useAuthStore();

  const email = formData.email;
  const emailError = errors.email;

  const validateEmail = (email: string): boolean => {
    const emailRegex =
      /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
    return emailRegex.test(email);
  };

  const handleSendOTP = async () => {
    if (!email) {
      setErrors({ ...errors, email: "Email is required" });
      return;
    }
    if (!validateEmail(email)) {
      setErrors({ ...errors, email: "Please enter a valid email address" });
      return;
    }

    try {
      const data = await authServices.requestPasswordReset(email);
      console.log("OTP sent:", data);
      setErrors({ ...errors, email: "" });
      setOtpSent(true);
    } catch (error: unknown) {
      const err = error as {
        response?: { status?: number; data?: { message?: string; errors?: string[] } };
        message?: string;
      };
      console.error("Error sending OTP:", err.response?.data || err.message);
      const errorData = err.response?.data || { message: err.message, errors: [] };
      let errorMsg = errorData.message || "Failed to send OTP";
      const specificErrors = Array.isArray(errorData.errors) ? errorData.errors : [];
      if (err.response?.status === 404) {
        errorMsg = "This email is not registered. Please sign up first.";
      }
      showDetailedErrorToast(errorMsg, specificErrors);
      setErrors({ ...errors, email: "" });
    }
  };

  const handleOtpChange = (index: number, value: string, isPaste = false) => {
    const numericValue = value.replace(/[^0-9]/g, "").slice(0, 1);
    let newOtp = [...otp];

    if (isPaste && value.length >= 5) {
      const pastedOtp = value
        .replace(/[^0-9]/g, "")
        .slice(0, 5)
        .split("");
      newOtp = pastedOtp.map((digit, i) => digit || otp[i] || "");
      setOtp(newOtp);
      if (newOtp.every((digit) => digit)) {
        const lastInput = document.getElementById(`otp-4`);
        if (lastInput) lastInput.focus();
      }
      return;
    }

    newOtp[index] = numericValue;
    setOtp(newOtp);
    setOtpError("");

    if (numericValue && index < 4) {
      const nextInput = document.getElementById(`otp-${index + 1}`);
      if (nextInput) nextInput.focus();
    } else if (!numericValue && index > 0) {
      const prevInput = document.getElementById(`otp-${index - 1}`);
      if (prevInput) prevInput.focus();
    } else if (!numericValue && index === 0) {
      newOtp[0] = "";
      setOtp(newOtp);
    }
  };

  const handleOtpSubmit = async () => {
    if (otp.some((digit) => !digit)) {
      setOtpError("All OTP digits are required");
      return;
    }
    if (otp.some((digit) => isNaN(parseInt(digit)))) {
      setOtpError("OTP must contain only numbers");
      return;
    }

    try {
      const otpValue = otp.join("");
      const data = await authServices.verifyOtp(email, otpValue);
      console.log("OTP verified:", data);
      setOtpError("");
      router.push("/auth/resetPassword");
    } catch (error: unknown) {
      const err = error as {
        response?: { data?: { message?: string; errors?: string[] } };
        message?: string;
      };
      console.error("Error verifying OTP:", err.response?.data || err.message);
      const errorData = err.response?.data || { message: err.message, errors: [] };
      const errorMsg = errorData.message || "Invalid OTP";
      const specificErrors = Array.isArray(errorData.errors) ? errorData.errors : [];
      showDetailedErrorToast(errorMsg, specificErrors);
      setOtpError("");
    }
  };

  const resetModal = (onClose: () => void) => {
    setForgotPasswordStep(1);
    setFormData({ email: "", password: "", confirmPassword: "" });
    setOtp(["", "", "", "", ""]);
    setErrors({ email: "", password: "", confirmPassword: "", name: "", companyName: "" });
    setOtpSent(false);
    setOtpError("");
    onClose();
  };

  return {
    step: forgotPasswordStep,
    email,
    setEmail: (value: string) => {
      setFormData({ email: value });
      setErrors({ ...errors, email: "" });
    },
    emailError,
    otp,
    otpSent,
    otpError,
    handleSendOTP,
    handleOtpChange,
    handleOtpSubmit,
    resetModal,
    setStep: setForgotPasswordStep,
    setOtpSent,
  };
};
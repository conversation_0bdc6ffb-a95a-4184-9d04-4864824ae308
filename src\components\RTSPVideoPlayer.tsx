"use client";
import React, { useRef, useEffect, useState } from "react";
import { Camera } from "@/lib/cameras";

interface RTSPVideoPlayerProps {
  camera: Camera;
  className?: string;
  onError?: (error: string) => void;
  onStreamLoad?: () => void;
}

const RTSPVideoPlayer: React.FC<RTSPVideoPlayerProps> = ({
  camera,
  className = "",
  onError,
  onStreamLoad,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [streamUrl, setStreamUrl] = useState<string | null>(null);

  useEffect(() => {
    if (!camera.url) {
      setHasError(true);
      setErrorMessage("No camera stream URL available");
      onError?.("No camera stream URL available");
      return;
    }

    const convertedUrl = convertRTSPToStreamUrl(camera.url);
    setStreamUrl(convertedUrl);
  }, [camera.url, onError]);

  const convertRTSPToStreamUrl = (rtspUrl: string): string => {
    if (rtspUrl.startsWith('http')) {
      return rtspUrl;
    }

    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:5000';
    const encodedRtsp = encodeURIComponent(rtspUrl);
    return `${baseUrl}/stream/hls?rtsp=${encodedRtsp}`;
  };

  const handleVideoLoad = () => {
    setIsLoading(false);
    setHasError(false);
    onStreamLoad?.();
  };

  const handleVideoError = (event: React.SyntheticEvent<HTMLVideoElement, Event>) => {
    console.error("Video stream error:", event);
    setIsLoading(false);
    setHasError(true);
    setErrorMessage("Failed to load camera stream");
    onError?.("Failed to load camera stream");
  };

  const handleVideoLoadStart = () => {
    setIsLoading(true);
    setHasError(false);
  };

  if (!streamUrl) {
    return (
      <div className={`flex items-center justify-center bg-gray-900 ${className}`}>
        <div className="text-center text-gray-400">
          <div className="mb-2"></div>
          <p className="text-sm">No Camera Found</p>
          <p className="text-xs">Invalid IP/Camera ID</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-900 z-10">
          <div className="text-center text-gray-400">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
            <p className="text-sm">Loading Stream...</p>
          </div>
        </div>
      )}

      {hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-900 z-10">
          <div className="text-center text-red-400">
            <div className="mb-2"></div>
            <p className="text-sm">No Camera Found</p>
            <p className="text-xs">{errorMessage}</p>
          </div>
        </div>
      )}

      <video
        ref={videoRef}
        className={`w-full h-full object-cover ${hasError ? 'hidden' : ''}`}
        autoPlay
        muted
        playsInline
        controls={false}
        onLoadStart={handleVideoLoadStart}
        onLoadedData={handleVideoLoad}
        onError={handleVideoError}
      >
        <source src={streamUrl} type="application/x-mpegURL" />
        <source src={streamUrl} type="video/mp4" />
        Your browser does not support the video tag.
      </video>

      {/* Live indicator */}
      {!isLoading && !hasError && (
        <div className="absolute top-2 left-2 bg-red-600 text-white px-2 py-1 rounded text-xs font-medium">
          LIVE
        </div>
      )}

      {/* Camera info overlay */}
      {!isLoading && !hasError && (
        <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs">
          {camera.name}
        </div>
      )}
    </div>
  );
};

export default RTSPVideoPlayer;

"use client";
import React from "react";
import { User } from "@/types/userTypes";
import UserActionsDropdown from "../UserActionsDropdown";

interface UserTableRowProps {
  user: User;
  isSelected: boolean;
  onSelect: (userId: string) => void;
  onEditUser: (user: User) => void;
  onRemoveUser: (user: User) => void;
  isCurrentAdmin?: boolean;
  isViewerAdmin?: boolean;
}

const UserTableRow: React.FC<UserTableRowProps> = ({
  user,
  isSelected,
  onSelect,
  onEditUser,
  onRemoveUser,
  isCurrentAdmin = false,
  isViewerAdmin = false,
}) => {
  return (
    <div
      className={`grid gap-2 px-6 py-4 transition-colors border-b border-[#3D3D3D] ${
        isCurrentAdmin
          ? "bg-[#1A1A1A]"
          : "hover:bg-[#2A2A2A]"
      }`}
      style={{
        gridTemplateColumns: "40px 150px 1fr 120px 120px 150px",
      }}
    >
      <div className="flex items-center">
        {!isCurrentAdmin && (
          <input
            type="checkbox"
            checked={isSelected}
            onChange={() => onSelect(user.id)}
            className="w-4 h-4 bg-[#3D3D3D] border border-[#5D5D5D] rounded focus:ring-[#E4E7EC] focus:ring-2"
            aria-label={`Select ${user.name}`}
          />
        )}
      </div>
      <div className="text-sm text-[#E4E7EC] flex items-center">
        <span className="truncate">{user.name}</span>
      </div>
      <div className="text-sm text-[#A3A3A3] truncate">
        {user.email}
      </div>
      <div className="text-sm text-[#E4E7EC]">{user.role}</div>
      <div className="text-sm text-[#A3A3A3]">{user.status}</div>
      <div className="text-sm text-[#A3A3A3] flex items-center justify-between">
        <span>{user.dateAdded}</span>
        {!isCurrentAdmin && user.role !== "Admin" && isViewerAdmin && (
          <UserActionsDropdown
            onEditUser={() => onEditUser(user)}
            onRemoveUser={() => onRemoveUser(user)}
            userName={user.name}
          />
        )}
      </div>
    </div>
  );
};

export default UserTableRow;

"use client";
import React from "react";

export const TabButton = ({
  tab,
  activeTab,
  setActiveTab,
}: {
  tab: "Sign Up" | "Sign In";
  activeTab: string;
  setActiveTab: (tab: "Sign Up" | "Sign In") => void;
}) => (
  <button
    type="button"
    className={`px-4 py-2 text-2xl flex-1 text-center cursor-pointer ${
      activeTab === tab ? "border-b-2 border-[#707070]" : "text-[#8A8A8A]"
    }`}
    onClick={() => setActiveTab(tab)}
  >
    {tab}
  </button>
);

// SubmitButton component
export const SubmitButton = ({
  label,
  className,
  icon: Icon,
  iconPosition = "left",
  showIcon = false,
  ...props
}: {
  label: string;
  icon?: React.ElementType;
  iconPosition?: "left" | "right";
  showIcon?: boolean;
} & React.ButtonHTMLAttributes<HTMLButtonElement>) => (
  <button
    type="submit"
    className={`w-48 bg-[#A3A3A3] cursor-pointer font-medium shadow-[4px_4px_4px_0px_#707070_inset,_0px_0px_5px_0px_#A3A3A380] border border-[#242424] text-[#262626] py-3 mt-6 transition-colors flex items-center justify-center gap-2 ${className || ""}`}
    onClick={(e) => {
      console.log("Submit button clicked!");
      if (props.onClick) {
        props.onClick(e);
      }
    }}
    {...props}
  >
    {showIcon && Icon && iconPosition === "left" && <Icon size={16} />}
    {label}
    {showIcon && Icon && iconPosition === "right" && <Icon size={16} />}
  </button>
);

// IconButton component
export const IconButton = ({
  label,
  icon: Icon,
  onClick,
  ...props
}: {
  label: string;
  icon?: React.ElementType;
  onClick?: () => void;
} & React.ButtonHTMLAttributes<HTMLButtonElement>) => (
  <button
    type="submit"
    className="group w-48 bg-[#A3A3A3] cursor-pointer font-medium shadow-[4px_4px_4px_0px_#707070_inset,_0px_0px_5px_0px_#A3A3A380] border border-[#242424] text-[#262626] py-3 mt-6 transition-colors flex items-center justify-center gap-2"
    onClick={onClick}
    {...props}
  >
    {label}
    {Icon && <Icon className="ml-2 group-hover:hidden" size={18} />}
  </button>
);

// BlurredButton component
interface BlurredButtonProps {
  onClick?: React.MouseEventHandler<HTMLButtonElement>;
  children: React.ReactNode;
  className?: string;
}

export const BlurredButton: React.FC<BlurredButtonProps> = ({
  onClick,
  children,
  className = "",
}) => (
  <button
    type="button"
    onClick={onClick}
    className={`cursor-pointer bg-[#1F1F1F] text-white text-sm border border-solid px-5 py-[2px] gradient-border ${className}`}
  >
    {children}
  </button>
);

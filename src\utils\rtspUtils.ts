export interface RTSPConfig {
  ipAddress: string;
  username?: string;
  password?: string;
  port?: number;
  path?: string;
}

export const generateRTSPUrl = (config: RTSPConfig): string => {
  const {
    ipAddress,
    username = 'admin',
    password = 'admin',
    port = 554,
    path = '/stream'
  } = config;

  return `rtsp://${username}:${password}@${ipAddress}:${port}${path}`;
};

export const isValidIPAddress = (ip: string): boolean => {
  const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  return ipRegex.test(ip);
};

export const isValidCameraId = (id: string): boolean => {
  const idRegex = /^[a-zA-Z0-9_-]+$/;
  return idRegex.test(id) && id.length >= 3 && id.length <= 50;
};

export const getInputType = (input: string): 'ip' | 'id' | 'invalid' => {
  if (isValidIPAddress(input)) {
    return 'ip';
  } else if (isValidCameraId(input)) {
    return 'id';
  } else {
    return 'invalid';
  }
};

export const convertRTSPToHLS = (rtspUrl: string, baseApiUrl: string): string => {
  if (rtspUrl.startsWith('http')) {
    return rtspUrl;
  }

  const encodedRtsp = encodeURIComponent(rtspUrl);
  return `${baseApiUrl}/stream/hls?rtsp=${encodedRtsp}`;
};

export const getCameraRTSPPaths = (manufacturer: string): string[] => {
  const paths: Record<string, string[]> = {
    'hikvision': ['/Streaming/Channels/101', '/h264/ch1/main/av_stream'],
    'dahua': ['/cam/realmonitor?channel=1&subtype=0', '/h264/ch1/main/av_stream'],
    'axis': ['/axis-media/media.amp', '/mjpg/video.mjpg'],
    'foscam': ['/videoMain', '/video.cgi'],
    'generic': ['/stream', '/live', '/video', '/cam/realmonitor?channel=1&subtype=0'],
  };

  return paths[manufacturer.toLowerCase()] || paths['generic'];
};

export const generateRTSPVariants = (config: RTSPConfig, manufacturer: string = 'generic'): string[] => {
  const paths = getCameraRTSPPaths(manufacturer);
  const variants: string[] = [];

  paths.forEach(path => {
    variants.push(generateRTSPUrl({ ...config, path }));
  });

  return variants;
};

export const validateCameraStream = async (streamUrl: string): Promise<boolean> => {
  return new Promise((resolve) => {
    const video = document.createElement('video');
    video.muted = true;
    video.playsInline = true;

    const timeout = setTimeout(() => {
      video.remove();
      resolve(false);
    }, 10000);

    video.onloadeddata = () => {
      clearTimeout(timeout);
      video.remove();
      resolve(true);
    };

    video.onerror = () => {
      clearTimeout(timeout);
      video.remove();
      resolve(false);
    };

    video.src = streamUrl;
    video.load();
  });
};

export const formatCameraError = (error: unknown): { message: string; suggestions: string[] } => {
  const err = error as { response?: { status?: number; data?: { message?: string } }; message?: string };
  const errorCode = err.response?.status;
  const errorMessage = err.response?.data?.message || err.message;

  switch (errorCode) {
    case 404:
      return {
        message: "No camera found at this address",
        suggestions: [
          "Verify the IP address is correct",
          "Check if camera is powered on",
          "Ensure camera is on the same network"
        ]
      };
    case 401:
    case 403:
      return {
        message: "Authentication failed",
        suggestions: [
          "Check username and password",
          "Verify camera credentials",
          "Try default credentials (admin/admin)"
        ]
      };
    case 408:
    case 504:
      return {
        message: "Connection timeout",
        suggestions: [
          "Camera is not responding",
          "Check network connectivity",
          "Verify camera is online"
        ]
      };
    default:
      return {
        message: errorMessage || "Failed to connect to camera",
        suggestions: [
          "Check camera configuration",
          "Verify network settings",
          "Try again in a few moments"
        ]
      };
  }
};

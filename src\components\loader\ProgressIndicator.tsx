import React, { useEffect, useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";

export default function ProgressIndicator() {
  const [progress, setProgress] = useState(0);
  const [currentText, setCurrentText] = useState("Smart Monitoring...");
  const router = useRouter();

  useEffect(() => {
    let animationFrameId: number;
    let startTime: number | null = null;
    const duration = 8000;

    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const elapsedTime = timestamp - startTime;
      const progress = Math.min((elapsedTime / duration) * 100, 100);

      setProgress(progress);

      if (progress >= 100) {
        router.push("/auth");
      } else {
        animationFrameId = requestAnimationFrame(animate);
      }
    };

    animationFrameId = requestAnimationFrame(animate);

    const textInterval = setInterval(() => {
      const texts = ["Smart Monitoring...", "Uncompromised Security", "Seamless Integration"];
      setCurrentText((prevText) => {
        const currentIndex = texts.indexOf(prevText);
        const nextIndex = (currentIndex + 1) % texts.length;
        return texts[nextIndex];
      });
    }, 2500);

    return () => {
      cancelAnimationFrame(animationFrameId);
      clearInterval(textInterval);
    };
  }, [router]);

  return (
    <div className="w-full">
      <div className="w-full h-[10px] bg-[#1F1F1F] mb-2 relative">
        <div
          className="h-full bg-[#3D3D3D] transition-all duration-75 ease-linear"
          style={{ width: `${progress}%` }}
        ></div>
        <div
          className="absolute top-1/2 transform -translate-y-1/2 transition-all duration-75 ease-linear"
          style={{ left: `${progress}%` }}
        >
          <Image
            src="/loader-indicator.svg"
            alt="Loading indicator"
            width={22}
            height={12}
            priority
          />
        </div>
      </div>

      <p className="text-sm text-center pt-5 text-[#E4E7EC]">
        {currentText}
      </p>
    </div>
  );
}
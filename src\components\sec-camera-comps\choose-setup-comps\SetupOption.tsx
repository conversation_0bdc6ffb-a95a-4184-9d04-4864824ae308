"use client";
import React from "react";

interface SetupOptionProps {
  title: string;
  subtitle: string;
  method: string;
  selectedMethod: string | null;
  onSelect: (method: string) => void;
}

const SetupOption: React.FC<SetupOptionProps> = ({
  title,
  subtitle,
  method,
  selectedMethod,
  onSelect,
}) => {
  const isSelected = selectedMethod === method;

  return (
    <div
      className="px-6 py-3 mb-6 border text-white border-solid gradient-border cursor-pointer"
      onClick={() => onSelect(method)}
    >
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-white font-medium mb-1">{title}</h2>
          <p className="text-[#8A8A8A] text-sm">{subtitle}</p>
        </div>
        <div
          className={`w-6 h-6 rounded-full border-2 ${
            isSelected
              ? "border-[#A3A3A3] border-[0.5px]"
              : "border-[#8A8A8A] border-[0.2px] bg-[#1F1F1F]"
          }`}
          style={!isSelected ? { boxShadow: "0px 0px 4px 0px #70707080" } : {}}
        >
          {isSelected && (
            <div className="flex items-center justify-center w-full h-full">
              <div
                className="w-2.5 h-2.5 bg-[#A3A3A3] rounded-full"
                style={{
                  boxShadow:
                    "4px 4px 4px 0px #707070 inset, 0px 0px 5px 0px #A3A3A380",
                }}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SetupOption;

import { userService } from "./userServices";

export interface InvitationData {
  email: string;
  role: string;
  invitedBy: string;
  organizationName: string;
  message: string;
  expiresAt: string;
}

// Extract invitation token from URL
export const getInvitationTokenFromUrl = (): string | null => {
  if (typeof window === 'undefined') return null;
  
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get('token');
};

// Validate invitation token and get invitation data
export const validateInvitationToken = async (token: string): Promise<InvitationData | null> => {
  try {
    const response = await userService.validateInvitationToken(token);
    if (response.result) {
      return response.result as InvitationData;
    }
    return null;
  } catch (error: unknown) {
    const err = error as {
      response?: {
        data?: { message?: string; status?: string };
        status?: number;
      };
      message?: string;
    };

    console.error("Error validating invitation token:", err);

    // Handle specific error cases
    if (err.response?.status === 400 || err.response?.status === 404) {
      const message = err.response?.data?.message || "";

      if (message.toLowerCase().includes("revoked")) {
        throw new Error("This invitation has been revoked by the administrator.");
      } else if (message.toLowerCase().includes("expired")) {
        throw new Error("This invitation has expired. Please contact your administrator for a new invitation.");
      } else if (message.toLowerCase().includes("already accepted") || message.toLowerCase().includes("used")) {
        throw new Error("This invitation has already been used. If you already have an account, please sign in instead.");
      } else if (message.toLowerCase().includes("invalid") || message.toLowerCase().includes("not found")) {
        throw new Error("This invitation link is invalid. Please check the link or contact your administrator.");
      }
    }

    // Generic error for other cases
    throw new Error("Unable to validate invitation. Please contact your administrator.");
  }
};

// Accept invitation and create account
export const acceptInvitation = async (data: {
  token: string;
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  companyName: string;
}): Promise<{ success: boolean; message?: string; error?: string }> => {
  try {
    console.log("Sending invitation acceptance request with data:", data);
    const response = await userService.acceptInvitation(data);
    console.log("Invitation acceptance response:", response);
    return {
      success: true,
      message: response.message,
    };
  } catch (error: unknown) {
    const err = error as {
      response?: {
        data?: { message?: string };
        status?: number;
        headers?: unknown;
      };
      message?: string;
    };
    console.error("Error accepting invitation:", err);
    console.error("Error response data:", err.response?.data);
    console.error("Error status:", err.response?.status);
    console.error("Error headers:", err.response?.headers);

    // Handle specific error cases for invitation acceptance
    let errorMessage = "Failed to accept invitation";

    if (err.response?.data?.message) {
      const message = err.response.data.message.toLowerCase();

      if (message.includes("revoked")) {
        errorMessage = "This invitation has been revoked by the administrator. Please contact them for a new invitation.";
      } else if (message.includes("expired")) {
        errorMessage = "This invitation has expired. Please contact your administrator for a new invitation.";
      } else if (message.includes("already accepted") || message.includes("used")) {
        errorMessage = "This invitation has already been used. If you already have an account, please sign in instead.";
      } else if (message.includes("invalid") || message.includes("not found")) {
        errorMessage = "This invitation is invalid. Please check the invitation link or contact your administrator.";
      } else {
        errorMessage = err.response.data.message;
      }
    }

    return {
      success: false,
      error: errorMessage,
    };
  }
};

// Clear invitation token from URL
export const clearInvitationTokenFromUrl = (): void => {
  if (typeof window === 'undefined') return;
  
  const url = new URL(window.location.href);
  url.searchParams.delete('token');
  window.history.replaceState({}, '', url.toString());
};
